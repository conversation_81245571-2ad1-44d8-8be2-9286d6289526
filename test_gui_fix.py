#!/usr/bin/env python3
"""
Test the fixed SAM GUI
"""

import sys
import os
from pathlib import Path

def main():
    print("Testing SAM GUI fix...")
    
    try:
        from sam_gui_advanced import SAMGUIAdvanced
        print("✓ SAM GUI import successful")
    except ImportError as e:
        print(f"✗ SAM GUI import failed: {e}")
        return 1
    
    try:
        from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig
        print("✓ SAM backend import successful")
    except ImportError as e:
        print(f"✗ SAM backend import failed: {e}")
        return 1
    
    # Test configuration
    try:
        sam_config = SAMConfig(
            checkpoint_path="sam_vit_h_4b8939.pth",
            device="cpu"
        )
        print("✓ SAM config creation successful")
    except Exception as e:
        print(f"✗ SAM config creation failed: {e}")
        return 1
    
    try:
        processing_config = ProcessingConfig(
            input_directory="CVH-seed-pic",
            output_directory="output",
            preview_mode=True,
            max_preview_images=5,
            save_debug_images=True,
            create_yolo_annotations=True,
            min_seed_area=100,
            max_seed_area=50000,
            min_aspect_ratio=0.3,
            max_aspect_ratio=3.0,
            min_solidity=0.8
        )
        print("✓ Processing config creation successful")
    except Exception as e:
        print(f"✗ Processing config creation failed: {e}")
        return 1
    
    print("\n✓ All tests passed! The GUI fix should work correctly.")
    print("\nTo start the GUI, run:")
    print("  conda activate cv")
    print("  python sam_gui_advanced.py")
    print("\nOr double-click: start_sam_gui.bat")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
