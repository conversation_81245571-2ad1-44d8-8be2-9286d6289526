#!/usr/bin/env python3
"""
YOLOE种子检测器
基于YOLOE (Real-Time Seeing Anything) 模型的种子检测后端
"""

import os
import sys
import json
import logging
import cv2
import numpy as np
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Callable
import re

# 检查YOLOE可用性
YOLOE_AVAILABLE = False
try:
    import torch
    import torchvision
    from ultralytics import YOLO
    YOLOE_AVAILABLE = True
except ImportError as e:
    print(f"YOLOE dependencies not available: {e}")
    print("Please install: pip install ultralytics torch torchvision")

@dataclass
class YOLOEConfig:
    """YOLOE模型配置"""
    model_path: str = "yolov8n.pt"  # 预训练模型路径
    device: str = "auto"  # 设备选择: "cpu", "cuda", "auto"
    confidence: float = 0.25  # 置信度阈值
    iou_threshold: float = 0.45  # NMS IoU阈值
    max_detections: int = 1000  # 最大检测数量
    image_size: int = 640  # 输入图像大小

@dataclass
class ProcessingConfig:
    """处理配置"""
    input_directory: str
    output_directory: str
    preview_mode: bool = False
    max_preview_images: int = 5
    save_debug_images: bool = True
    create_yolo_annotations: bool = True
    
    # 种子过滤参数
    min_seed_area: int = 100
    max_seed_area: int = 50000
    min_aspect_ratio: float = 0.2
    max_aspect_ratio: float = 5.0
    min_confidence: float = 0.3  # 最小置信度

class YOLOESeedDetector:
    """基于YOLOE的种子检测器"""

    def __init__(self, yoloe_config: YOLOEConfig, processing_config: ProcessingConfig, 
                 progress_callback=None, log_callback=None):
        self.yoloe_config = yoloe_config
        self.proc_config = processing_config
        self.progress_callback = progress_callback
        self.log_callback = log_callback

        # 处理控制
        self.is_processing = False
        self.should_stop = False
        self.should_pause = False

        # 设置日志
        self.setup_logging()

        # 初始化YOLOE模型
        self.model = None
        if YOLOE_AVAILABLE:
            self.initialize_yoloe()
        else:
            self.logger.error("YOLOE not available. Please install ultralytics.")
            raise ImportError("ultralytics package not installed")

        # 统计信息
        self.processing_stats = {
            'total_images': 0,
            'total_seeds_found': 0,
            'species_counts': {},
            'processing_time': 0,
            'current_image': '',
            'processed_images': 0
        }

    def setup_logging(self):
        """设置日志"""
        log_dir = Path(self.proc_config.output_directory) / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"yoloe_processing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('YOLOESeedDetector')

    def _log_message(self, message, level='info'):
        """发送日志消息到GUI"""
        if self.log_callback:
            self.log_callback(message, level)

        # 同时记录到文件日志
        if level == 'info':
            self.logger.info(message)
        elif level == 'warning':
            self.logger.warning(message)
        elif level == 'error':
            self.logger.error(message)

    def _update_progress(self, current, total, current_image='', seeds_found=0):
        """更新进度到GUI"""
        if self.progress_callback:
            progress_data = {
                'current': current,
                'total': total,
                'percentage': (current / total * 100) if total > 0 else 0,
                'current_image': current_image,
                'seeds_found': seeds_found,
                'total_seeds': self.processing_stats['total_seeds_found']
            }
            self.progress_callback(progress_data)

    def initialize_yoloe(self):
        """初始化YOLOE模型"""
        try:
            # 设置设备
            if self.yoloe_config.device == "auto":
                device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                device = self.yoloe_config.device
            
            self.logger.info(f"Initializing YOLOE model on device: {device}")
            
            # 加载YOLOE模型
            self.model = YOLO(self.yoloe_config.model_path)
            self.model.to(device)
            
            self.logger.info("YOLOE model initialized successfully")
            self._log_message("YOLOE模型初始化成功")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize YOLOE model: {e}")
            self._log_message(f"YOLOE模型初始化失败: {e}", 'error')
            raise

    def extract_species_id(self, filename: str) -> str:
        """从文件名提取物种ID"""
        match = re.search(r'S(\d+)-', filename)
        return match.group(1) if match else "unknown"

    def detect_seeds_with_yoloe(self, image: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """使用YOLOE进行种子检测"""
        try:
            self.logger.info("Starting YOLOE detection...")
            self._log_message("开始YOLOE检测...")
            
            # 运行YOLOE检测
            results = self.model(
                image,
                conf=self.yoloe_config.confidence,
                iou=self.yoloe_config.iou_threshold,
                max_det=self.yoloe_config.max_detections,
                imgsz=self.yoloe_config.image_size,
                verbose=False
            )
            
            # 提取检测结果
            detections = []
            if len(results) > 0 and results[0].boxes is not None:
                boxes = results[0].boxes
                for i in range(len(boxes)):
                    # 获取边界框坐标 (xyxy格式)
                    x1, y1, x2, y2 = boxes.xyxy[i].cpu().numpy()
                    confidence = boxes.conf[i].cpu().numpy()
                    
                    # 转换为xywh格式
                    x, y, w, h = x1, y1, x2-x1, y2-y1
                    area = w * h
                    
                    detection = {
                        'bbox': [x, y, w, h],
                        'confidence': float(confidence),
                        'area': float(area),
                        'class_id': 0  # 假设所有检测都是种子类别
                    }
                    detections.append(detection)
            
            self.logger.info(f"YOLOE detected {len(detections)} objects")
            self._log_message(f"YOLOE检测到 {len(detections)} 个对象")
            
            # 过滤检测结果
            filtered_detections = self.filter_detections(detections, image.shape[:2])
            
            self.logger.info(f"After filtering: {len(filtered_detections)} seed detections")
            self._log_message(f"过滤后: {len(filtered_detections)} 个种子检测")
            
            # 创建调试图像
            debug_image = self.create_debug_image(image, filtered_detections)
            
            return filtered_detections, debug_image
            
        except Exception as e:
            self.logger.error(f"YOLOE detection failed: {e}")
            self._log_message(f"YOLOE检测失败: {e}", 'error')
            return [], image.copy()

    def filter_detections(self, detections: List[Dict], image_shape: Tuple[int, int]) -> List[Dict]:
        """过滤检测结果以获得种子"""
        if not detections:
            self.logger.warning("No detections to filter")
            self._log_message("没有检测结果需要过滤", 'warning')
            return []

        self.logger.info(f"Filtering {len(detections)} detections with parameters:")
        self.logger.info(f"  Area: {self.proc_config.min_seed_area} - {self.proc_config.max_seed_area}")
        self.logger.info(f"  Aspect ratio: {self.proc_config.min_aspect_ratio} - {self.proc_config.max_aspect_ratio}")
        self.logger.info(f"  Confidence: >= {self.proc_config.min_confidence}")

        self._log_message(f"开始过滤 {len(detections)} 个检测结果")
        self._log_message(f"过滤参数 - 面积: {self.proc_config.min_seed_area}-{self.proc_config.max_seed_area}, "
                         f"长宽比: {self.proc_config.min_aspect_ratio}-{self.proc_config.max_aspect_ratio}, "
                         f"置信度: >={self.proc_config.min_confidence}")

        filtered_detections = []
        rejection_stats = {
            'area_too_small': 0,
            'area_too_large': 0,
            'aspect_ratio_too_small': 0,
            'aspect_ratio_too_large': 0,
            'confidence_too_low': 0
        }

        for i, detection in enumerate(detections):
            bbox = detection['bbox']  # [x, y, w, h]
            area = detection['area']
            confidence = detection['confidence']
            
            # 计算纵横比
            aspect_ratio = max(bbox[2], bbox[3]) / min(bbox[2], bbox[3]) if min(bbox[2], bbox[3]) > 0 else float('inf')

            # 检查过滤条件
            rejected = False
            rejection_reasons = []

            if area < self.proc_config.min_seed_area:
                rejected = True
                rejection_reasons.append(f"area too small ({area:.0f} < {self.proc_config.min_seed_area})")
                rejection_stats['area_too_small'] += 1
            elif area > self.proc_config.max_seed_area:
                rejected = True
                rejection_reasons.append(f"area too large ({area:.0f} > {self.proc_config.max_seed_area})")
                rejection_stats['area_too_large'] += 1

            if aspect_ratio < self.proc_config.min_aspect_ratio:
                rejected = True
                rejection_reasons.append(f"aspect ratio too small ({aspect_ratio:.2f} < {self.proc_config.min_aspect_ratio})")
                rejection_stats['aspect_ratio_too_small'] += 1
            elif aspect_ratio > self.proc_config.max_aspect_ratio:
                rejected = True
                rejection_reasons.append(f"aspect ratio too large ({aspect_ratio:.2f} > {self.proc_config.max_aspect_ratio})")
                rejection_stats['aspect_ratio_too_large'] += 1

            if confidence < self.proc_config.min_confidence:
                rejected = True
                rejection_reasons.append(f"confidence too low ({confidence:.2f} < {self.proc_config.min_confidence})")
                rejection_stats['confidence_too_low'] += 1

            if not rejected:
                # 添加额外信息
                detection['aspect_ratio'] = aspect_ratio
                filtered_detections.append(detection)
                self.logger.debug(f"✓ Accepted detection {i}: area={area:.0f}, aspect_ratio={aspect_ratio:.2f}, confidence={confidence:.2f}")
            else:
                self.logger.debug(f"✗ Rejected detection {i}: {'; '.join(rejection_reasons)}")

        # 按置信度排序（高的在前）
        filtered_detections.sort(key=lambda x: x['confidence'], reverse=True)

        # 记录过滤统计
        self.logger.info(f"Filtering completed: {len(filtered_detections)}/{len(detections)} detections passed")
        self._log_message(f"过滤完成: {len(filtered_detections)}/{len(detections)} 个检测通过")

        if len(filtered_detections) == 0:
            self.logger.warning("No detections passed filtering. Rejection statistics:")
            self._log_message("没有检测通过过滤。拒绝统计:", 'warning')
            for reason, count in rejection_stats.items():
                if count > 0:
                    self.logger.warning(f"  {reason}: {count}")
                    self._log_message(f"  {reason}: {count}", 'warning')

        return filtered_detections

    def create_debug_image(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """创建调试图像"""
        debug_image = image.copy()

        if len(detections) == 0:
            # 没有检测到种子
            cv2.putText(debug_image, "No seeds detected with YOLOE",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            return debug_image

        # 绘制检测到的种子
        for i, detection in enumerate(detections):
            bbox = detection['bbox']
            confidence = detection['confidence']

            # 获取边界框坐标
            x, y, w, h = bbox
            x1, y1, x2, y2 = int(x), int(y), int(x + w), int(y + h)

            # 生成颜色
            color = (0, 255, 0)  # 绿色

            # 绘制边界框
            cv2.rectangle(debug_image, (x1, y1), (x2, y2), color, 2)

            # 添加标签
            label = f"Seed {i+1}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(debug_image, (x1, y1 - label_size[1] - 10),
                         (x1 + label_size[0], y1), color, -1)
            cv2.putText(debug_image, label, (x1, y1 - 5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # 添加总数信息
        cv2.putText(debug_image, f"YOLOE detected: {len(detections)} seeds",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        return debug_image

    def extract_seed_crops(self, image: np.ndarray, detections: List[Dict],
                          base_filename: str, species_id: str) -> List[str]:
        """提取种子裁剪图像"""
        crops_dir = Path(self.proc_config.output_directory) / "crops" / f"species_{species_id}"
        crops_dir.mkdir(parents=True, exist_ok=True)

        saved_files = []

        for i, detection in enumerate(detections):
            bbox = detection['bbox']
            confidence = detection['confidence']

            # 扩展边界框以包含更多上下文
            x, y, w, h = bbox
            padding = 20
            x1 = max(0, int(x - padding))
            y1 = max(0, int(y - padding))
            x2 = min(image.shape[1], int(x + w + padding))
            y2 = min(image.shape[0], int(y + h + padding))

            # 提取裁剪区域
            crop_image = image[y1:y2, x1:x2].copy()

            # 生成文件名
            crop_filename = f"{base_filename}_seed_{i:03d}_conf{confidence:.2f}.jpg"
            crop_path = crops_dir / crop_filename

            # 保存裁剪图像
            success = cv2.imwrite(str(crop_path), crop_image)
            if success:
                saved_files.append(str(crop_path))
                self.logger.info(f"Saved seed crop: {crop_path}")
                self._log_message(f"已保存种子裁剪: {crop_path.name}")
            else:
                self.logger.error(f"Failed to save crop: {crop_path}")
                self._log_message(f"保存裁剪失败: {crop_path.name}", 'error')

        return saved_files

    def create_yolo_annotations(self, detections: List[Dict], image_shape: Tuple[int, int],
                               species_id: str) -> str:
        """创建YOLO格式的注释"""
        if not self.proc_config.create_yolo_annotations:
            return ""

        height, width = image_shape[:2]
        annotations = []

        # 假设所有种子属于同一类别（可以根据species_id映射）
        class_id = 0  # 可以根据需要修改

        for detection in detections:
            bbox = detection['bbox']
            x, y, w, h = bbox

            # 转换为YOLO格式（归一化的中心坐标和宽高）
            center_x = (x + w / 2) / width
            center_y = (y + h / 2) / height
            norm_width = w / width
            norm_height = h / height

            annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}")

        return "\n".join(annotations)

    def process_single_image(self, image_path: str) -> Dict:
        """处理单个图像"""
        try:
            filename = Path(image_path).name
            base_filename = Path(image_path).stem
            species_id = self.extract_species_id(filename)

            self.logger.info(f"Processing {filename} (Species: {species_id})")
            self._log_message(f"开始处理图像: {filename}")

            # 加载图像
            self.logger.info(f"Loading image: {image_path}")
            image = cv2.imread(image_path)
            if image is None:
                error_msg = f"Cannot load image: {image_path}"
                self.logger.error(error_msg)
                self._log_message(f"图像加载失败: {filename}", 'error')
                raise ValueError(error_msg)

            h, w, c = image.shape
            self.logger.info(f"Image loaded successfully: {w}x{h}x{c}")
            self._log_message(f"图像加载成功: {w}x{h} 像素")

            # 使用YOLOE进行检测
            self.logger.info("Starting YOLOE detection...")
            self._log_message("开始YOLOE检测...")
            detections, debug_image = self.detect_seeds_with_yoloe(image)

            self.logger.info(f"YOLOE detection completed, found {len(detections)} valid detections")
            self._log_message(f"YOLOE检测完成，找到 {len(detections)} 个有效检测")

            if len(detections) == 0:
                self.logger.warning(f"No seeds found in {filename}")
                self._log_message(f"在 {filename} 中未找到种子", 'warning')

                # 仍然保存调试图像以便分析
                debug_image_path = None
                if debug_image is not None and self.proc_config.save_debug_images:
                    debug_dir = Path(self.proc_config.output_directory) / "debug"
                    debug_dir.mkdir(parents=True, exist_ok=True)
                    debug_path = debug_dir / f"yoloe_debug_{filename}"
                    cv2.imwrite(str(debug_path), debug_image)
                    debug_image_path = str(debug_path)
                    self.logger.info(f"Saved debug image (no seeds): {debug_path}")
                    self._log_message(f"已保存调试图像: {debug_path.name}")

                return {
                    'filename': filename,
                    'species_id': species_id,
                    'seeds_found': 0,
                    'success': True,
                    'saved_files': [],
                    'yolo_annotation': "",
                    'debug_image_path': debug_image_path
                }

            # 提取种子裁剪
            self.logger.info("Extracting seed crops...")
            self._log_message("提取种子裁剪图像...")
            saved_files = self.extract_seed_crops(image, detections, base_filename, species_id)

            # 创建YOLO注释
            self.logger.info("Creating YOLO annotations...")
            yolo_annotation = self.create_yolo_annotations(detections, image.shape, species_id)

            # 保存YOLO注释文件
            if yolo_annotation:
                ann_dir = Path(self.proc_config.output_directory) / "annotations"
                ann_dir.mkdir(parents=True, exist_ok=True)
                ann_path = ann_dir / f"{base_filename}.txt"
                with open(ann_path, 'w') as f:
                    f.write(yolo_annotation)
                self.logger.info(f"Saved YOLO annotation: {ann_path}")
                self._log_message(f"已保存YOLO注释: {ann_path.name}")

            # 保存调试图像
            debug_image_path = None
            if debug_image is not None and self.proc_config.save_debug_images:
                debug_dir = Path(self.proc_config.output_directory) / "debug"
                debug_dir.mkdir(parents=True, exist_ok=True)
                debug_path = debug_dir / f"yoloe_debug_{filename}"
                cv2.imwrite(str(debug_path), debug_image)
                debug_image_path = str(debug_path)
                self.logger.info(f"Saved debug image: {debug_path}")
                self._log_message(f"已保存调试图像: {debug_path.name}")

            # 更新统计
            self.processing_stats['total_seeds_found'] += len(detections)
            if species_id not in self.processing_stats['species_counts']:
                self.processing_stats['species_counts'][species_id] = 0
            self.processing_stats['species_counts'][species_id] += len(detections)

            self.logger.info(f"Successfully processed {filename}: {len(detections)} seeds found")
            self._log_message(f"成功处理 {filename}: 找到 {len(detections)} 个种子")

            return {
                'filename': filename,
                'species_id': species_id,
                'seeds_found': len(detections),
                'success': True,
                'saved_files': saved_files,
                'yolo_annotation': yolo_annotation,
                'debug_image_path': debug_image_path,
                'detections': detections
            }

        except Exception as e:
            self.logger.error(f"Error processing {image_path}: {e}")
            self._log_message(f"处理图像失败 {Path(image_path).name}: {e}", 'error')
            return {
                'filename': Path(image_path).name,
                'species_id': 'unknown',
                'seeds_found': 0,
                'success': False,
                'error': str(e),
                'saved_files': [],
                'yolo_annotation': "",
                'debug_image_path': None
            }

    def start_processing(self):
        """开始处理"""
        self.is_processing = True
        self.should_stop = False
        self.should_pause = False

    def pause_processing(self):
        """暂停处理"""
        self.should_pause = True

    def resume_processing(self):
        """恢复处理"""
        self.should_pause = False

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
        self.is_processing = False

    def get_processing_stats(self) -> Dict:
        """获取处理统计信息"""
        return self.processing_stats.copy()

# 主函数用于测试
if __name__ == "__main__":
    # 测试YOLOE检测器
    print("Testing YOLOE Seed Detector...")

    if not YOLOE_AVAILABLE:
        print("YOLOE not available. Please install ultralytics.")
        sys.exit(1)

    # 配置
    yoloe_config = YOLOEConfig(
        model_path="yolov8n.pt",
        device="cpu",
        confidence=0.25
    )

    processing_config = ProcessingConfig(
        input_directory="CVH-seed-pic",
        output_directory="test_yoloe_output"
    )

    # 创建检测器
    detector = YOLOESeedDetector(yoloe_config, processing_config)

    # 测试单个图像
    test_images = list(Path("CVH-seed-pic").glob("*.jpg"))[:1]
    if test_images:
        result = detector.process_single_image(str(test_images[0]))
        print(f"Test result: {result}")
    else:
        print("No test images found in CVH-seed-pic directory")
