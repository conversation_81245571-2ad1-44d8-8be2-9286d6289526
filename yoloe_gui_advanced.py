#!/usr/bin/env python3
"""
YOLOE种子检测GUI - 高级版本
基于YOLOE (Real-Time Seeing Anything) 模型的种子检测图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import sys
import threading
import time
from pathlib import Path
from datetime import datetime
import shutil
import subprocess

# 检查后端可用性
BACKEND_AVAILABLE = False
YOLOE_AVAILABLE = False

try:
    from yoloe_seed_detector import YOLOESeedDetector, YOLOEConfig, ProcessingConfig, YOLOE_AVAILABLE
    BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"Backend not available: {e}")

class SessionManager:
    """会话管理器"""
    
    def __init__(self, base_output_dir="output"):
        self.base_output_dir = Path(base_output_dir)
        self.current_session_id = None
        self.current_session_dir = None
    
    def create_new_session(self):
        """创建新会话"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_id = f"session_{timestamp}"
        session_dir = self.base_output_dir / session_id
        
        # 创建会话目录结构
        session_dir.mkdir(parents=True, exist_ok=True)
        (session_dir / "crops").mkdir(exist_ok=True)
        (session_dir / "annotations").mkdir(exist_ok=True)
        (session_dir / "debug").mkdir(exist_ok=True)
        (session_dir / "models").mkdir(exist_ok=True)
        (session_dir / "logs").mkdir(exist_ok=True)
        
        self.current_session_id = session_id
        self.current_session_dir = session_dir
        
        return session_id, session_dir
    
    def get_session_list(self):
        """获取会话列表"""
        if not self.base_output_dir.exists():
            return []
        
        sessions = []
        for item in self.base_output_dir.iterdir():
            if item.is_dir() and item.name.startswith('session_'):
                sessions.append(item.name)
        
        return sorted(sessions, reverse=True)  # 最新的在前
    
    def load_session(self, session_id):
        """加载现有会话"""
        self.current_session_id = session_id
        self.current_session_dir = self.base_output_dir / session_id
        return self.current_session_dir

class ChineseTextManager:
    """中文文本管理器"""
    
    def __init__(self):
        self.texts = {
            'app_title': 'YOLOE种子检测工具',
            'file_settings': '文件设置',
            'parameters': '参数设置',
            'processing_control': '处理控制',
            'preview': '结果预览',
            'yolo_training': 'YOLO训练',
            'yolo_detection': 'YOLO检测',
            'input_dir': '输入图像目录:',
            'output_dir': '输出目录:',
            'model_path': 'YOLOE模型路径:',
            'device': '设备:',
            'browse': '浏览',
            'confidence': '置信度阈值:',
            'iou_threshold': 'IoU阈值:',
            'image_size': '图像大小:',
            'min_seed_area': '最小种子面积:',
            'max_seed_area': '最大种子面积:',
            'min_aspect_ratio': '最小长宽比:',
            'max_aspect_ratio': '最大长宽比:',
            'min_confidence': '最小置信度:',
            'preview_mode': '预览模式',
            'max_preview_images': '最大预览图像数:',
            'save_debug_images': '保存调试图像',
            'create_yolo_annotations': '创建YOLO注释',
            'start_processing': '开始处理',
            'pause': '暂停',
            'stop': '停止',
            'current_session': '当前会话:',
            'new_session': '新建会话',
            'load_session': '加载会话',
            'progress': '进度:',
            'current_image': '当前图像:',
            'processed_count': '已处理:',
            'seeds_detected': '检测到的种子:',
            'processing_log': '处理日志',
            'clear_log': '清空日志',
            'open_output_dir': '打开输出目录',
            'save_config': '保存配置',
            'load_config': '加载配置',
            'reset_config': '重置配置'
        }
    
    def get_text(self, key):
        """获取文本"""
        return self.texts.get(key, key)

class YOLOEGUIAdvanced:
    """YOLOE高级GUI应用程序"""

    def __init__(self, root):
        self.root = root
        self.config_file = "yoloe_gui_config.json"

        # 初始化管理器
        self.text_mgr = ChineseTextManager()
        self.session_mgr = SessionManager("output")

        # 加载配置
        self.load_config()

        # 初始化变量
        self.init_variables()

        # 设置GUI
        self.setup_gui()

        # 创建初始会话
        self.create_new_session()

        # 处理控制
        self.is_processing = False
        self.processing_thread = None
        self.detector = None

        # 记录初始消息
        self.log_message("YOLOE高级GUI初始化成功")

    def load_config(self):
        """从文件加载配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except Exception as e:
            print(f"配置加载错误: {e}")
            self.config = {}

    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'input_dir': self.input_dir.get(),
                'output_dir': self.output_dir.get(),
                'model_path': self.model_path.get(),
                'device': self.device.get(),
                'confidence': self.confidence.get(),
                'iou_threshold': self.iou_threshold.get(),
                'image_size': self.image_size.get(),
                'min_seed_area': self.min_seed_area.get(),
                'max_seed_area': self.max_seed_area.get(),
                'min_aspect_ratio': self.min_aspect_ratio.get(),
                'max_aspect_ratio': self.max_aspect_ratio.get(),
                'min_confidence': self.min_confidence.get(),
                'preview_mode': self.preview_mode.get(),
                'max_preview_images': self.max_preview_images.get(),
                'save_debug_images': self.save_debug_images.get(),
                'create_yolo_annotations': self.create_yolo_annotations.get()
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.log_message("配置已保存")
        except Exception as e:
            self.log_message(f"保存配置失败: {e}", 'error')

    def init_variables(self):
        """初始化GUI变量"""
        # 文件路径
        self.input_dir = tk.StringVar(value=self.config.get('input_dir', 'CVH-seed-pic'))
        self.output_dir = tk.StringVar(value=self.config.get('output_dir', 'output'))
        self.model_path = tk.StringVar(value=self.config.get('model_path', 'yolov8n.pt'))
        self.device = tk.StringVar(value=self.config.get('device', 'auto'))

        # YOLOE参数
        self.confidence = tk.DoubleVar(value=self.config.get('confidence', 0.25))
        self.iou_threshold = tk.DoubleVar(value=self.config.get('iou_threshold', 0.45))
        self.image_size = tk.IntVar(value=self.config.get('image_size', 640))

        # 种子过滤参数
        self.min_seed_area = tk.IntVar(value=self.config.get('min_seed_area', 100))
        self.max_seed_area = tk.IntVar(value=self.config.get('max_seed_area', 50000))
        self.min_aspect_ratio = tk.DoubleVar(value=self.config.get('min_aspect_ratio', 0.2))
        self.max_aspect_ratio = tk.DoubleVar(value=self.config.get('max_aspect_ratio', 5.0))
        self.min_confidence = tk.DoubleVar(value=self.config.get('min_confidence', 0.3))

        # 处理选项
        self.preview_mode = tk.BooleanVar(value=self.config.get('preview_mode', True))
        self.max_preview_images = tk.IntVar(value=self.config.get('max_preview_images', 10))
        self.save_debug_images = tk.BooleanVar(value=self.config.get('save_debug_images', True))
        self.create_yolo_annotations = tk.BooleanVar(value=self.config.get('create_yolo_annotations', True))

        # 会话管理
        self.current_session_id = tk.StringVar()

        # 进度显示
        self.progress_var = tk.DoubleVar()
        self.current_image_var = tk.StringVar(value="无")
        self.processed_count = tk.IntVar()
        self.total_count = tk.IntVar()
        self.seeds_detected = tk.IntVar()

    def setup_gui(self):
        """设置GUI界面"""
        self.root.title(self.text_mgr.get_text('app_title'))
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)

        # 创建主界面
        self.create_main_interface()

        # 创建状态栏
        self.create_status_bar()

    def create_main_interface(self):
        """创建主界面"""
        # 创建笔记本控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 文件设置标签页
        self.create_file_settings_tab()

        # 参数设置标签页
        self.create_parameters_tab()

        # 处理控制标签页
        self.create_processing_tab()

        # 结果预览标签页
        self.create_preview_tab()

        # YOLO训练标签页
        self.create_yolo_training_tab()

        # YOLO检测标签页
        self.create_yolo_detection_tab()

    def create_file_settings_tab(self):
        """创建文件设置标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('file_settings'))

        # 会话管理
        session_frame = ttk.LabelFrame(frame, text="会话管理")
        session_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(session_frame, text=self.text_mgr.get_text('current_session')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(session_frame, textvariable=self.current_session_id).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Button(session_frame, text=self.text_mgr.get_text('new_session'), command=self.create_new_session).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(session_frame, text=self.text_mgr.get_text('load_session'), command=self.load_session).grid(row=0, column=3, padx=5, pady=5)

        # 文件路径设置
        paths_frame = ttk.LabelFrame(frame, text="文件路径")
        paths_frame.pack(fill=tk.X, padx=5, pady=5)

        # 输入目录
        ttk.Label(paths_frame, text=self.text_mgr.get_text('input_dir')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.text_mgr.get_text('browse'), command=self.browse_input_dir).grid(row=0, column=2, padx=5, pady=5)

        # 输出目录
        ttk.Label(paths_frame, text=self.text_mgr.get_text('output_dir')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.text_mgr.get_text('browse'), command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=5)

        # YOLOE模型路径
        ttk.Label(paths_frame, text=self.text_mgr.get_text('model_path')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(paths_frame, textvariable=self.model_path, width=50).grid(row=2, column=1, padx=5, pady=5)
        ttk.Button(paths_frame, text=self.text_mgr.get_text('browse'), command=self.browse_model_path).grid(row=2, column=2, padx=5, pady=5)

        # 设备选择
        device_frame = ttk.LabelFrame(frame, text="设备设置")
        device_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(device_frame, text=self.text_mgr.get_text('device')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        device_combo = ttk.Combobox(device_frame, textvariable=self.device, values=['auto', 'cpu', 'cuda'], state='readonly')
        device_combo.grid(row=0, column=1, padx=5, pady=5)

        # 配置管理
        config_frame = ttk.LabelFrame(frame, text="配置管理")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(config_frame, text=self.text_mgr.get_text('save_config'), command=self.save_config).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(config_frame, text=self.text_mgr.get_text('load_config'), command=self.load_config_file).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(config_frame, text=self.text_mgr.get_text('reset_config'), command=self.reset_config).pack(side=tk.LEFT, padx=5, pady=5)

    def create_parameters_tab(self):
        """创建参数设置标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('parameters'))

        # YOLOE参数
        yoloe_frame = ttk.LabelFrame(frame, text="YOLOE检测参数")
        yoloe_frame.pack(fill=tk.X, padx=5, pady=5)

        # 置信度阈值
        ttk.Label(yoloe_frame, text=self.text_mgr.get_text('confidence')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        confidence_scale = ttk.Scale(yoloe_frame, from_=0.1, to=0.9, variable=self.confidence, orient=tk.HORIZONTAL, length=200)
        confidence_scale.grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(yoloe_frame, textvariable=self.confidence).grid(row=0, column=2, padx=5, pady=5)

        # IoU阈值
        ttk.Label(yoloe_frame, text=self.text_mgr.get_text('iou_threshold')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        iou_scale = ttk.Scale(yoloe_frame, from_=0.1, to=0.9, variable=self.iou_threshold, orient=tk.HORIZONTAL, length=200)
        iou_scale.grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(yoloe_frame, textvariable=self.iou_threshold).grid(row=1, column=2, padx=5, pady=5)

        # 图像大小
        ttk.Label(yoloe_frame, text=self.text_mgr.get_text('image_size')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        size_combo = ttk.Combobox(yoloe_frame, textvariable=self.image_size, values=[320, 416, 512, 640, 832, 1024], state='readonly')
        size_combo.grid(row=2, column=1, padx=5, pady=5)

        # 种子过滤参数
        filter_frame = ttk.LabelFrame(frame, text="种子过滤参数")
        filter_frame.pack(fill=tk.X, padx=5, pady=5)

        # 最小种子面积
        ttk.Label(filter_frame, text=self.text_mgr.get_text('min_seed_area')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.min_seed_area, width=10).grid(row=0, column=1, padx=5, pady=5)

        # 最大种子面积
        ttk.Label(filter_frame, text=self.text_mgr.get_text('max_seed_area')).grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.max_seed_area, width=10).grid(row=0, column=3, padx=5, pady=5)

        # 最小长宽比
        ttk.Label(filter_frame, text=self.text_mgr.get_text('min_aspect_ratio')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.min_aspect_ratio, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 最大长宽比
        ttk.Label(filter_frame, text=self.text_mgr.get_text('max_aspect_ratio')).grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(filter_frame, textvariable=self.max_aspect_ratio, width=10).grid(row=1, column=3, padx=5, pady=5)

        # 最小置信度
        ttk.Label(filter_frame, text=self.text_mgr.get_text('min_confidence')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        min_conf_scale = ttk.Scale(filter_frame, from_=0.1, to=0.9, variable=self.min_confidence, orient=tk.HORIZONTAL, length=200)
        min_conf_scale.grid(row=2, column=1, columnspan=2, padx=5, pady=5)
        ttk.Label(filter_frame, textvariable=self.min_confidence).grid(row=2, column=3, padx=5, pady=5)

        # 处理选项
        options_frame = ttk.LabelFrame(frame, text="处理选项")
        options_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Checkbutton(options_frame, text=self.text_mgr.get_text('preview_mode'), variable=self.preview_mode).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(options_frame, text=self.text_mgr.get_text('max_preview_images')).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(options_frame, textvariable=self.max_preview_images, width=10).grid(row=0, column=2, padx=5, pady=5)

        ttk.Checkbutton(options_frame, text=self.text_mgr.get_text('save_debug_images'), variable=self.save_debug_images).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Checkbutton(options_frame, text=self.text_mgr.get_text('create_yolo_annotations'), variable=self.create_yolo_annotations).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

    def create_processing_tab(self):
        """创建处理控制标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('processing_control'))

        # 控制按钮
        control_frame = ttk.LabelFrame(frame, text="处理控制")
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_button = ttk.Button(control_frame, text=self.text_mgr.get_text('start_processing'), command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.pause_button = ttk.Button(control_frame, text=self.text_mgr.get_text('pause'), command=self.pause_processing, state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=5, pady=5)

        self.stop_button = ttk.Button(control_frame, text=self.text_mgr.get_text('stop'), command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5, pady=5)

        # 进度显示
        progress_frame = ttk.LabelFrame(frame, text="处理进度")
        progress_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(progress_frame, text=self.text_mgr.get_text('progress')).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100, length=300)
        self.progress_bar.grid(row=0, column=1, columnspan=2, padx=5, pady=5)

        ttk.Label(progress_frame, text=self.text_mgr.get_text('current_image')).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.current_image_label = ttk.Label(progress_frame, textvariable=self.current_image_var)
        self.current_image_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)

        ttk.Label(progress_frame, text=self.text_mgr.get_text('processed_count')).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(progress_frame, textvariable=self.processed_count).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(progress_frame, text="/").grid(row=2, column=2, sticky=tk.W, padx=2, pady=5)
        ttk.Label(progress_frame, textvariable=self.total_count).grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)

        ttk.Label(progress_frame, text=self.text_mgr.get_text('seeds_detected')).grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Label(progress_frame, textvariable=self.seeds_detected).grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)

        # 日志显示
        log_frame = ttk.LabelFrame(frame, text=self.text_mgr.get_text('processing_log'))
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_control_frame, text=self.text_mgr.get_text('clear_log'), command=self.clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_control_frame, text=self.text_mgr.get_text('open_output_dir'), command=self.open_output_dir).pack(side=tk.LEFT, padx=5)

        # 日志文本框
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = tk.Text(log_text_frame, height=15, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_preview_tab(self):
        """创建结果预览标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('preview'))

        # 预览控制
        preview_control_frame = ttk.Frame(frame)
        preview_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(preview_control_frame, text="刷新预览", command=self.refresh_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(preview_control_frame, text="上一张", command=self.prev_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(preview_control_frame, text="下一张", command=self.next_preview).pack(side=tk.LEFT, padx=5)

        # 预览显示区域
        preview_frame = ttk.LabelFrame(frame, text="图像预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 这里可以添加图像预览功能
        self.preview_label = ttk.Label(preview_frame, text="预览区域 - 处理完成后显示结果")
        self.preview_label.pack(expand=True)

    def create_yolo_training_tab(self):
        """创建YOLO训练标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('yolo_training'))

        # 训练数据准备
        data_frame = ttk.LabelFrame(frame, text="训练数据准备")
        data_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(data_frame, text="准备训练数据", command=self.prepare_training_data).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(data_frame, text="验证数据集", command=self.validate_dataset).pack(side=tk.LEFT, padx=5, pady=5)

        # 训练参数
        train_params_frame = ttk.LabelFrame(frame, text="训练参数")
        train_params_frame.pack(fill=tk.X, padx=5, pady=5)

        self.epochs = tk.IntVar(value=100)
        self.batch_size = tk.IntVar(value=16)
        self.learning_rate = tk.DoubleVar(value=0.01)

        ttk.Label(train_params_frame, text="训练轮数:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(train_params_frame, textvariable=self.epochs, width=10).grid(row=0, column=1, padx=5, pady=5)

        ttk.Label(train_params_frame, text="批次大小:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(train_params_frame, textvariable=self.batch_size, width=10).grid(row=0, column=3, padx=5, pady=5)

        ttk.Label(train_params_frame, text="学习率:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(train_params_frame, textvariable=self.learning_rate, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 训练控制
        train_control_frame = ttk.LabelFrame(frame, text="训练控制")
        train_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(train_control_frame, text="开始训练", command=self.start_training).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(train_control_frame, text="停止训练", command=self.stop_training).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(train_control_frame, text="查看训练日志", command=self.view_training_log).pack(side=tk.LEFT, padx=5, pady=5)

    def create_yolo_detection_tab(self):
        """创建YOLO检测标签页"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text=self.text_mgr.get_text('yolo_detection'))

        # 模型选择
        model_frame = ttk.LabelFrame(frame, text="模型选择")
        model_frame.pack(fill=tk.X, padx=5, pady=5)

        self.detection_model_path = tk.StringVar(value="yolov8n.pt")
        ttk.Label(model_frame, text="检测模型:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(model_frame, textvariable=self.detection_model_path, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(model_frame, text="浏览", command=self.browse_detection_model).grid(row=0, column=2, padx=5, pady=5)

        # 检测控制
        detection_control_frame = ttk.LabelFrame(frame, text="检测控制")
        detection_control_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(detection_control_frame, text="开始检测", command=self.start_detection).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(detection_control_frame, text="加载结果", command=self.load_detection_results).pack(side=tk.LEFT, padx=5, pady=5)

        # 检测结果显示
        results_frame = ttk.LabelFrame(frame, text="检测结果")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.results_text = tk.Text(results_frame, height=10, wrap=tk.WORD)
        results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)

        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)

        # 后端状态指示
        backend_status = "YOLOE可用" if YOLOE_AVAILABLE else "YOLOE不可用"
        self.backend_label = ttk.Label(self.status_bar, text=backend_status)
        self.backend_label.pack(side=tk.RIGHT, padx=5, pady=2)

    # 事件处理方法
    def browse_input_dir(self):
        """浏览输入目录"""
        directory = filedialog.askdirectory(title="选择输入图像目录")
        if directory:
            self.input_dir.set(directory)

    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_dir.set(directory)

    def browse_model_path(self):
        """浏览模型文件"""
        filename = filedialog.askopenfilename(
            title="选择YOLOE模型文件",
            filetypes=[("PyTorch模型", "*.pt"), ("所有文件", "*.*")]
        )
        if filename:
            self.model_path.set(filename)

    def browse_detection_model(self):
        """浏览检测模型文件"""
        filename = filedialog.askopenfilename(
            title="选择检测模型文件",
            filetypes=[("PyTorch模型", "*.pt"), ("所有文件", "*.*")]
        )
        if filename:
            self.detection_model_path.set(filename)

    def create_new_session(self):
        """创建新会话"""
        session_id, session_dir = self.session_mgr.create_new_session()
        self.current_session_id.set(session_id)
        self.log_message(f"创建新会话: {session_id}")

    def load_session(self):
        """加载现有会话"""
        sessions = self.session_mgr.get_session_list()
        if not sessions:
            messagebox.showinfo("信息", "没有找到现有会话")
            return

        # 创建会话选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("选择会话")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="选择要加载的会话:").pack(pady=10)

        listbox = tk.Listbox(dialog)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for session in sessions:
            listbox.insert(tk.END, session)

        def load_selected():
            selection = listbox.curselection()
            if selection:
                selected_session = sessions[selection[0]]
                self.session_mgr.load_session(selected_session)
                self.current_session_id.set(selected_session)
                self.log_message(f"加载会话: {selected_session}")
                dialog.destroy()

        ttk.Button(dialog, text="加载", command=load_selected).pack(pady=5)
        ttk.Button(dialog, text="取消", command=dialog.destroy).pack(pady=5)

    def load_config_file(self):
        """加载配置文件"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.set_config_values(config)
                self.log_message(f"配置已加载: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {e}")

    def set_config_values(self, config):
        """设置配置值"""
        for key, value in config.items():
            if hasattr(self, key):
                var = getattr(self, key)
                if isinstance(var, tk.Variable):
                    var.set(value)

    def reset_config(self):
        """重置配置"""
        if messagebox.askyesno("确认", "确定要重置所有配置吗？"):
            # 重置为默认值
            self.confidence.set(0.25)
            self.iou_threshold.set(0.45)
            self.image_size.set(640)
            self.min_seed_area.set(100)
            self.max_seed_area.set(50000)
            self.min_aspect_ratio.set(0.2)
            self.max_aspect_ratio.set(5.0)
            self.min_confidence.set(0.3)
            self.preview_mode.set(True)
            self.max_preview_images.set(10)
            self.save_debug_images.set(True)
            self.create_yolo_annotations.set(True)
            self.log_message("配置已重置为默认值")

    def log_message(self, message, level='info'):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据级别设置颜色
        if level == 'error':
            color = 'red'
        elif level == 'warning':
            color = 'orange'
        else:
            color = 'black'

        # 插入消息
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")

        # 滚动到底部
        self.log_text.see(tk.END)

        # 更新状态栏
        self.status_label.config(text=message)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_output_dir(self):
        """打开输出目录"""
        if self.session_mgr.current_session_dir and self.session_mgr.current_session_dir.exists():
            if sys.platform == "win32":
                os.startfile(self.session_mgr.current_session_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", str(self.session_mgr.current_session_dir)])
            else:
                subprocess.run(["xdg-open", str(self.session_mgr.current_session_dir)])
        else:
            messagebox.showwarning("警告", "输出目录不存在")

    def start_processing(self):
        """开始处理"""
        if not BACKEND_AVAILABLE:
            messagebox.showerror("错误", "YOLOE后端不可用，请检查安装")
            return

        if not YOLOE_AVAILABLE:
            messagebox.showerror("错误", "YOLOE不可用，请安装ultralytics")
            return

        # 验证输入
        if not Path(self.input_dir.get()).exists():
            messagebox.showerror("错误", "输入目录不存在")
            return

        if not Path(self.model_path.get()).exists():
            messagebox.showerror("错误", "模型文件不存在")
            return

        # 重置统计
        self.processed_count.set(0)
        self.total_count.set(0)
        self.seeds_detected.set(0)
        self.progress_var.set(0)

        # 启动处理线程
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.NORMAL)

        self.processing_thread = threading.Thread(target=self._process_images)
        self.processing_thread.daemon = True
        self.processing_thread.start()

        self.log_message("开始处理")

    def pause_processing(self):
        """暂停处理"""
        if self.detector:
            self.detector.pause_processing()
        self.log_message("处理已暂停")

    def stop_processing(self):
        """停止处理"""
        if self.detector:
            self.detector.stop_processing()
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.DISABLED)
        self.log_message("处理已停止")

    def _process_images(self):
        """处理图像（在后台线程中运行）"""
        try:
            if not BACKEND_AVAILABLE:
                self.log_message("YOLOE后端不可用")
                return

            input_path = Path(self.input_dir.get())
            image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))

            if not image_files:
                self.log_message("输入目录中没有找到图像文件")
                return

            self.total_count.set(len(image_files))

            # 限制预览模式下的图像数量
            if self.preview_mode.get():
                image_files = image_files[:self.max_preview_images.get()]
                self.log_message(f"预览模式: 处理 {len(image_files)} 张图像")

            # 初始化YOLOE检测器
            try:
                yoloe_config = YOLOEConfig(
                    model_path=self.model_path.get(),
                    device=self.device.get(),
                    confidence=self.confidence.get(),
                    iou_threshold=self.iou_threshold.get(),
                    image_size=self.image_size.get()
                )

                processing_config = ProcessingConfig(
                    input_directory=self.input_dir.get(),
                    output_directory=str(self.session_mgr.current_session_dir),
                    preview_mode=self.preview_mode.get(),
                    max_preview_images=self.max_preview_images.get(),
                    save_debug_images=self.save_debug_images.get(),
                    create_yolo_annotations=self.create_yolo_annotations.get(),
                    min_seed_area=self.min_seed_area.get(),
                    max_seed_area=self.max_seed_area.get(),
                    min_aspect_ratio=self.min_aspect_ratio.get(),
                    max_aspect_ratio=self.max_aspect_ratio.get(),
                    min_confidence=self.min_confidence.get()
                )

                self.detector = YOLOESeedDetector(yoloe_config, processing_config,
                                                 progress_callback=self._update_progress_gui,
                                                 log_callback=self._log_callback)
                self.log_message("YOLOE检测器初始化成功")

            except Exception as e:
                self.log_message(f"初始化YOLOE失败: {e}", 'error')
                return

            # 处理每张图像
            for i, image_file in enumerate(image_files):
                if not self.is_processing:
                    break

                self.current_image_var.set(image_file.name)
                self.processed_count.set(i + 1)

                # 更新进度
                progress = (i + 1) / len(image_files) * 100
                self.progress_var.set(progress)

                try:
                    # 使用YOLOE处理图像
                    result = self.detector.process_single_image(str(image_file))

                    if result and result.get('success', False):
                        detected_seeds = result.get('seeds_found', 0)
                        self.seeds_detected.set(self.seeds_detected.get() + detected_seeds)
                        self.log_message(f"处理 {image_file.name}: 检测到 {detected_seeds} 个种子")
                    else:
                        self.log_message(f"处理 {image_file.name}: 检测到 0 个种子")

                except Exception as e:
                    self.log_message(f"处理 {image_file.name} 时出错: {e}", 'error')

            if self.is_processing:
                self.log_message("处理完成")
                self.refresh_preview()

        except Exception as e:
            self.log_message(f"处理错误: {e}", 'error')
        finally:
            self.is_processing = False
            self.start_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.DISABLED)

    def _update_progress_gui(self, progress_data):
        """更新GUI进度（从后台线程调用）"""
        # 使用after方法在主线程中更新GUI
        self.root.after(0, self._update_progress_main_thread, progress_data)

    def _update_progress_main_thread(self, progress_data):
        """在主线程中更新进度"""
        self.progress_var.set(progress_data['percentage'])
        self.current_image_var.set(progress_data['current_image'])
        self.seeds_detected.set(progress_data['total_seeds'])

    def _log_callback(self, message, level='info'):
        """日志回调（从后台线程调用）"""
        self.root.after(0, self.log_message, message, level)

    # 预览相关方法
    def refresh_preview(self):
        """刷新预览"""
        self.log_message("刷新预览")
        # TODO: 实现预览功能

    def prev_preview(self):
        """上一张预览"""
        self.log_message("上一张预览")
        # TODO: 实现预览导航

    def next_preview(self):
        """下一张预览"""
        self.log_message("下一张预览")
        # TODO: 实现预览导航

    # YOLO训练相关方法
    def prepare_training_data(self):
        """准备训练数据"""
        self.log_message("准备训练数据")
        # TODO: 实现训练数据准备

    def validate_dataset(self):
        """验证数据集"""
        self.log_message("验证数据集")
        # TODO: 实现数据集验证

    def start_training(self):
        """开始训练"""
        self.log_message("开始YOLO训练")
        # TODO: 实现YOLO训练

    def stop_training(self):
        """停止训练"""
        self.log_message("停止YOLO训练")
        # TODO: 实现训练停止

    def view_training_log(self):
        """查看训练日志"""
        self.log_message("查看训练日志")
        # TODO: 实现训练日志查看

    # YOLO检测相关方法
    def start_detection(self):
        """开始检测"""
        self.log_message("开始YOLO检测")
        # TODO: 实现独立的YOLO检测

    def load_detection_results(self):
        """加载检测结果"""
        self.log_message("加载检测结果")
        # TODO: 实现检测结果加载

def main():
    """主函数"""
    # 检查环境
    if not BACKEND_AVAILABLE:
        print("警告: YOLOE后端不可用")
        print("请确保已安装 yoloe_seed_detector.py")

    if not YOLOE_AVAILABLE:
        print("警告: YOLOE不可用")
        print("请安装: pip install ultralytics torch torchvision")

    # 创建GUI
    root = tk.Tk()
    app = YOLOEGUIAdvanced(root)

    # 显示关于信息
    app.log_message("YOLOE种子检测工具启动")
    app.log_message("版本: 1.0")
    app.log_message("基于YOLOE (Real-Time Seeing Anything) 模型")
    app.log_message("")
    app.log_message("功能特点:")
    app.log_message("• 实时种子检测")
    app.log_message("• 会话管理和输出组织")
    app.log_message("• 手动参数调节")
    app.log_message("• 独立的YOLO训练模块")
    app.log_message("• 独立的YOLO检测模块")
    app.log_message("• 增强的结果预览系统")
    app.log_message("")

    if app.session_mgr.current_session_id:
        app.log_message(f"当前会话: {app.session_mgr.current_session_id}")

    # 运行GUI
    root.mainloop()

if __name__ == "__main__":
    main()
