#!/usr/bin/env python3
"""
Final test to verify everything is working
"""

import sys
import os
from pathlib import Path

def main():
    print("=== SAM环境最终测试 ===")
    
    # Test 1: Basic environment
    print("\n1. 基础环境测试:")
    print(f"   Python版本: {sys.version}")
    print(f"   当前目录: {os.getcwd()}")
    
    # Test 2: File existence
    print("\n2. 文件存在性测试:")
    required_files = [
        "sam_vit_h_4b8939.pth",
        "CVH-seed-pic", 
        "sam_gui_config.json",
        "sam_gui_advanced.py"
    ]
    
    all_files_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✓ {file_path}")
        else:
            print(f"   ✗ {file_path}")
            all_files_exist = False
    
    # Test 3: Package imports
    print("\n3. 包导入测试:")
    try:
        import cv2
        print("   ✓ OpenCV")
    except ImportError as e:
        print(f"   ✗ OpenCV: {e}")
        return 1
    
    try:
        import numpy as np
        print("   ✓ NumPy")
    except ImportError as e:
        print(f"   ✗ NumPy: {e}")
        return 1
    
    try:
        from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
        print("   ✓ Segment Anything")
    except ImportError as e:
        print(f"   ✗ Segment Anything: {e}")
        return 1
    
    try:
        import tkinter
        print("   ✓ Tkinter")
    except ImportError as e:
        print(f"   ✗ Tkinter: {e}")
        return 1
    
    # Test 4: SAM GUI import
    print("\n4. SAM GUI导入测试:")
    try:
        from sam_gui_advanced import SAMGUIAdvanced
        print("   ✓ SAM GUI Advanced")
    except ImportError as e:
        print(f"   ✗ SAM GUI Advanced: {e}")
        return 1
    
    # Test 5: Image count
    print("\n5. 图像文件测试:")
    if os.path.exists("CVH-seed-pic"):
        image_files = list(Path("CVH-seed-pic").glob("*.jpg")) + list(Path("CVH-seed-pic").glob("*.png"))
        print(f"   找到 {len(image_files)} 个图像文件")
    else:
        print("   ✗ CVH-seed-pic目录不存在")
        return 1
    
    print("\n=== 测试完成 ===")
    if all_files_exist:
        print("✓ 所有测试通过！SAM环境配置正确。")
        print("\n启动SAM GUI的方法:")
        print("1. 双击 start_sam_gui.bat")
        print("2. 或在命令行运行: conda activate cv && python sam_gui_advanced.py")
        return 0
    else:
        print("✗ 部分文件缺失，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
