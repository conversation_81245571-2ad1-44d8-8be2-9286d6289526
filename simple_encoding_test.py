#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 设置Windows控制台编码为UTF-8
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

def main():
    print("=" * 60)
    print("简单编码测试")
    print("=" * 60)
    
    print("[INFO] Python版本:", sys.version)
    print("[INFO] 平台:", sys.platform)
    print("[INFO] 当前工作目录:", os.getcwd())
    
    # 测试中文输出
    print("[TEST] 中文输出测试: 这是一个测试")
    
    # 测试文件检查
    test_files = ["sam_vit_h_4b8939.pth", "CVH-seed-pic", "sam_gui_config.json"]
    for file_path in test_files:
        if os.path.exists(file_path):
            print(f"[OK] 文件存在: {file_path}")
        else:
            print(f"[MISSING] 文件不存在: {file_path}")
    
    print("\n[SUCCESS] 编码测试完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
