#!/usr/bin/env python3
"""
详细的SAM处理调试脚本
用于诊断SAM处理失败的具体原因
"""

import sys
import os
from pathlib import Path
import json
import traceback
import cv2
import numpy as np

def test_image_loading(input_dir):
    """测试图像加载"""
    print("🔍 测试图像加载...")
    
    input_path = Path(input_dir)
    if not input_path.exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        return False, []
    
    # 查找图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print("❌ 未找到图像文件")
        return False, []
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 测试加载前几个图像
    loaded_images = []
    for i, img_file in enumerate(image_files[:3]):
        try:
            image = cv2.imread(str(img_file))
            if image is None:
                print(f"❌ 无法加载图像: {img_file.name}")
                continue
            
            h, w, c = image.shape
            file_size = img_file.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ {img_file.name}: {w}x{h}x{c}, {file_size:.1f}MB")
            
            # 检查图像是否过大或过小
            if w * h > 10000000:  # 10M pixels
                print(f"⚠️ 图像很大，可能导致内存问题: {w}x{h}")
            elif w * h < 10000:  # 10K pixels
                print(f"⚠️ 图像很小，可能影响检测效果: {w}x{h}")
            
            loaded_images.append((img_file, image))
            
        except Exception as e:
            print(f"❌ 加载图像失败 {img_file.name}: {e}")
    
    return len(loaded_images) > 0, loaded_images

def test_sam_initialization(model_path):
    """测试SAM模型初始化"""
    print("\n🔍 测试SAM模型初始化...")
    
    try:
        from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
        
        # 检查模型文件
        if not Path(model_path).exists():
            print(f"❌ 模型文件不存在: {model_path}")
            return False, None
        
        print(f"✅ 模型文件存在: {model_path}")
        
        # 加载模型
        print("正在加载SAM模型...")
        sam_model = sam_model_registry["vit_h"](checkpoint=model_path)
        sam_model.to(device="cpu")
        print("✅ SAM模型加载成功")
        
        # 创建掩码生成器
        print("正在创建掩码生成器...")
        mask_generator = SamAutomaticMaskGenerator(
            model=sam_model,
            points_per_side=16,
            pred_iou_thresh=0.7,
            stability_score_thresh=0.8,
            min_mask_region_area=50,
        )
        print("✅ 掩码生成器创建成功")
        
        return True, mask_generator
        
    except Exception as e:
        print(f"❌ SAM初始化失败: {e}")
        traceback.print_exc()
        return False, None

def test_sam_segmentation(mask_generator, image_data):
    """测试SAM分割"""
    print("\n🔍 测试SAM分割...")
    
    img_file, image = image_data
    print(f"测试图像: {img_file.name}")
    
    try:
        print("正在生成掩码...")
        masks = mask_generator.generate(image)
        print(f"✅ SAM生成了 {len(masks)} 个掩码")
        
        if len(masks) == 0:
            print("⚠️ 未生成任何掩码")
            return False, []
        
        # 分析掩码
        print("\n📊 掩码分析:")
        areas = [mask['area'] for mask in masks]
        print(f"  掩码数量: {len(masks)}")
        print(f"  面积范围: {min(areas)} - {max(areas)} 像素")
        print(f"  平均面积: {np.mean(areas):.0f} 像素")
        
        # 显示前几个掩码的详细信息
        for i, mask in enumerate(masks[:5]):
            bbox = mask['bbox']
            area = mask['area']
            stability = mask['stability_score']
            iou = mask['predicted_iou']
            print(f"  掩码 {i+1}: 面积={area}, 稳定性={stability:.3f}, IoU={iou:.3f}, 边界框={bbox}")
        
        return True, masks
        
    except Exception as e:
        print(f"❌ SAM分割失败: {e}")
        traceback.print_exc()
        return False, []

def test_mask_filtering(masks, filter_params):
    """测试掩码过滤"""
    print("\n🔍 测试掩码过滤...")
    
    if not masks:
        print("❌ 没有掩码可以过滤")
        return []
    
    print(f"过滤前掩码数量: {len(masks)}")
    print(f"过滤参数: {filter_params}")
    
    filtered_masks = []
    rejection_reasons = {}
    
    for i, mask_data in enumerate(masks):
        mask = mask_data['segmentation']
        area = mask_data['area']
        bbox = mask_data['bbox']  # [x, y, w, h]
        
        # 计算纵横比
        aspect_ratio = max(bbox[2], bbox[3]) / min(bbox[2], bbox[3]) if min(bbox[2], bbox[3]) > 0 else float('inf')
        
        # 计算凸度（solidity）
        try:
            contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            if len(contours) > 0:
                contour = max(contours, key=cv2.contourArea)
                hull = cv2.convexHull(contour)
                hull_area = cv2.contourArea(hull)
                solidity = area / hull_area if hull_area > 0 else 0
            else:
                solidity = 0
        except Exception as e:
            print(f"⚠️ 计算凸度失败 (掩码 {i}): {e}")
            solidity = 0
        
        # 检查过滤条件
        reasons = []
        
        if area < filter_params['min_seed_area']:
            reasons.append(f"面积太小({area} < {filter_params['min_seed_area']})")
        elif area > filter_params['max_seed_area']:
            reasons.append(f"面积太大({area} > {filter_params['max_seed_area']})")
        
        if aspect_ratio < filter_params['min_aspect_ratio']:
            reasons.append(f"长宽比太小({aspect_ratio:.2f} < {filter_params['min_aspect_ratio']})")
        elif aspect_ratio > filter_params['max_aspect_ratio']:
            reasons.append(f"长宽比太大({aspect_ratio:.2f} > {filter_params['max_aspect_ratio']})")
        
        if solidity < filter_params['min_solidity']:
            reasons.append(f"实心度太低({solidity:.2f} < {filter_params['min_solidity']})")
        
        if not reasons:
            # 通过所有过滤条件
            mask_data['aspect_ratio'] = aspect_ratio
            mask_data['solidity'] = solidity
            filtered_masks.append(mask_data)
            print(f"  ✅ 掩码 {i}: 面积={area}, 长宽比={aspect_ratio:.2f}, 实心度={solidity:.2f}")
        else:
            # 记录拒绝原因
            reason_key = "; ".join(reasons)
            rejection_reasons[reason_key] = rejection_reasons.get(reason_key, 0) + 1
            if i < 10:  # 只显示前10个被拒绝的掩码
                print(f"  ❌ 掩码 {i}: {reason_key}")
    
    print(f"\n📊 过滤结果:")
    print(f"  通过过滤: {len(filtered_masks)} 个掩码")
    print(f"  被拒绝: {len(masks) - len(filtered_masks)} 个掩码")
    
    if rejection_reasons:
        print(f"\n📋 拒绝原因统计:")
        for reason, count in rejection_reasons.items():
            print(f"  {reason}: {count} 个")
    
    return filtered_masks

def main():
    """主函数"""
    print("=" * 60)
    print("SAM处理详细调试工具")
    print("=" * 60)
    
    # 获取配置
    config_file = "sam_gui_config.json"
    model_path = "sam_vit_h_4b8939.pth"
    input_dir = "input_images"
    
    if Path(config_file).exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            model_path = config.get('model_path', model_path)
            input_dir = config.get('input_dir', input_dir)
            print(f"📋 从配置文件加载设置: {config_file}")
        except Exception as e:
            print(f"⚠️ 配置文件读取失败: {e}")
    
    print(f"🔧 使用设置:")
    print(f"  SAM模型: {model_path}")
    print(f"  输入目录: {input_dir}")
    
    # 1. 测试图像加载
    success, loaded_images = test_image_loading(input_dir)
    if not success:
        print("\n❌ 图像加载失败，无法继续测试")
        return 1
    
    # 2. 测试SAM初始化
    success, mask_generator = test_sam_initialization(model_path)
    if not success:
        print("\n❌ SAM初始化失败，无法继续测试")
        return 1
    
    # 3. 测试SAM分割
    test_image = loaded_images[0]  # 使用第一个图像
    success, masks = test_sam_segmentation(mask_generator, test_image)
    if not success:
        print("\n❌ SAM分割失败")
        return 1
    
    # 4. 测试不同的过滤参数
    filter_configs = [
        {
            'name': '当前GUI参数',
            'min_seed_area': 100,
            'max_seed_area': 50000,
            'min_aspect_ratio': 0.3,
            'max_aspect_ratio': 3.0,
            'min_solidity': 0.8
        },
        {
            'name': '宽松参数',
            'min_seed_area': 50,
            'max_seed_area': 100000,
            'min_aspect_ratio': 0.1,
            'max_aspect_ratio': 10.0,
            'min_solidity': 0.3
        },
        {
            'name': '极宽松参数',
            'min_seed_area': 20,
            'max_seed_area': 200000,
            'min_aspect_ratio': 0.05,
            'max_aspect_ratio': 20.0,
            'min_solidity': 0.1
        }
    ]
    
    for filter_config in filter_configs:
        print(f"\n--- 测试 {filter_config['name']} ---")
        filtered_masks = test_mask_filtering(masks, filter_config)
        
        if len(filtered_masks) > 0:
            print(f"🎉 使用 {filter_config['name']} 检测到 {len(filtered_masks)} 个种子!")
            print("建议在GUI中使用这些参数:")
            for key, value in filter_config.items():
                if key != 'name':
                    print(f"  {key}: {value}")
            break
    else:
        print("\n❌ 所有过滤参数都无法检测到种子")
        print("可能的原因:")
        print("1. 图像中确实没有符合条件的种子")
        print("2. SAM模型对这类图像的分割效果不好")
        print("3. 需要调整SAM的分割参数（points_per_side, pred_iou_thresh等）")
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
