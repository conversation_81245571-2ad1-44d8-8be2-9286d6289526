#!/usr/bin/env python3
"""
基于Segment Anything Model (SAM) 的种子分割工具
SAM-based Seed Segmentation Tool

使用Meta的Segment Anything Model进行精确的种子分割和裁剪
Uses Meta's Segment Anything Model for precise seed segmentation and cropping
"""

import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from pathlib import Path
import json
import re
import logging
import traceback
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import argparse
from datetime import datetime

try:
    from segment_anything import sam_model_registry, SamAutomaticMaskGenerator, SamPredictor
    SAM_AVAILABLE = True
except ImportError:
    print("⚠️ Segment Anything not installed. Please install with: pip install segment-anything")
    SAM_AVAILABLE = False

@dataclass
class SAMConfig:
    """SAM模型配置"""
    model_type: str = "vit_h"  # vit_h, vit_l, vit_b
    checkpoint_path: str = "sam_vit_h_4b8939.pth"
    device: str = "auto"  # auto, cpu, cuda
    
    # 自动掩码生成参数
    points_per_side: int = 32
    pred_iou_thresh: float = 0.88
    stability_score_thresh: float = 0.95
    crop_n_layers: int = 1
    crop_n_points_downscale_factor: int = 2
    min_mask_region_area: int = 1000  # 最小掩码区域面积

@dataclass
class ProcessingConfig:
    """处理配置"""
    input_directory: str
    output_directory: str
    preview_mode: bool = False
    max_preview_images: int = 5
    save_debug_images: bool = True
    create_yolo_annotations: bool = True
    
    # 种子过滤参数
    min_seed_area: int = 1000
    max_seed_area: int = 500000
    min_aspect_ratio: float = 0.2
    max_aspect_ratio: float = 5.0
    min_solidity: float = 0.5  # 凸度：面积/凸包面积

class SAMSeedSegmenter:
    """基于SAM的种子分割器"""

    def __init__(self, sam_config: SAMConfig, processing_config: ProcessingConfig, progress_callback=None, log_callback=None):
        self.sam_config = sam_config
        self.proc_config = processing_config
        self.progress_callback = progress_callback  # GUI进度回调
        self.log_callback = log_callback  # GUI日志回调

        # 处理控制
        self.is_processing = False
        self.should_stop = False
        self.should_pause = False

        # 设置日志
        self.setup_logging()

        # 初始化SAM模型
        self.sam_model = None
        self.mask_generator = None
        self.predictor = None

        if SAM_AVAILABLE:
            self.initialize_sam()
        else:
            self.logger.error("SAM not available. Please install segment-anything.")
            raise ImportError("segment-anything package not installed")

        # 统计信息
        self.processing_stats = {
            'total_images': 0,
            'total_seeds_found': 0,
            'species_counts': {},
            'processing_time': 0,
            'current_image': '',
            'processed_images': 0
        }
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path(self.proc_config.output_directory) / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"sam_processing_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('SAMSeedSegmenter')

    def start_processing(self):
        """开始处理"""
        self.is_processing = True
        self.should_stop = False
        self.should_pause = False

    def pause_processing(self):
        """暂停处理"""
        self.should_pause = True

    def resume_processing(self):
        """恢复处理"""
        self.should_pause = False

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
        self.is_processing = False

    def _log_message(self, message, level='info'):
        """发送日志消息到GUI"""
        if self.log_callback:
            self.log_callback(message, level)

        # 同时记录到文件日志
        if level == 'info':
            self.logger.info(message)
        elif level == 'warning':
            self.logger.warning(message)
        elif level == 'error':
            self.logger.error(message)

    def _update_progress(self, current, total, current_image='', seeds_found=0):
        """更新进度到GUI"""
        if self.progress_callback:
            progress_data = {
                'current': current,
                'total': total,
                'percentage': (current / total * 100) if total > 0 else 0,
                'current_image': current_image,
                'seeds_found': seeds_found,
                'total_seeds': self.processing_stats['total_seeds_found']
            }
            self.progress_callback(progress_data)
    
    def initialize_sam(self):
        """初始化SAM模型"""
        try:
            # 检查模型文件
            if not Path(self.sam_config.checkpoint_path).exists():
                raise FileNotFoundError(f"SAM checkpoint not found: {self.sam_config.checkpoint_path}")
            
            # 设置设备
            if self.sam_config.device == "auto":
                device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                device = self.sam_config.device
            
            self.logger.info(f"Initializing SAM model on device: {device}")
            
            # 加载SAM模型
            self.sam_model = sam_model_registry[self.sam_config.model_type](
                checkpoint=self.sam_config.checkpoint_path
            )
            self.sam_model.to(device=device)
            
            # 创建自动掩码生成器
            self.mask_generator = SamAutomaticMaskGenerator(
                model=self.sam_model,
                points_per_side=self.sam_config.points_per_side,
                pred_iou_thresh=self.sam_config.pred_iou_thresh,
                stability_score_thresh=self.sam_config.stability_score_thresh,
                crop_n_layers=self.sam_config.crop_n_layers,
                crop_n_points_downscale_factor=self.sam_config.crop_n_points_downscale_factor,
                min_mask_region_area=self.sam_config.min_mask_region_area,
            )
            
            # 创建预测器（用于交互式分割）
            self.predictor = SamPredictor(self.sam_model)
            
            self.logger.info("SAM model initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize SAM model: {e}")
            raise
    
    def extract_species_id(self, filename: str) -> str:
        """从文件名提取物种ID"""
        match = re.search(r'S(\d+)-', filename)
        return match.group(1) if match else "unknown"
    
    def segment_seeds_with_sam(self, image: np.ndarray) -> Tuple[List[Dict], np.ndarray]:
        """使用SAM进行种子分割"""
        try:
            self.logger.info("Starting SAM segmentation...")
            
            # 生成掩码
            masks = self.mask_generator.generate(image)
            
            self.logger.info(f"SAM generated {len(masks)} masks")
            
            # 过滤掩码
            filtered_masks = self.filter_masks(masks, image.shape[:2])
            
            self.logger.info(f"After filtering: {len(filtered_masks)} seed masks")
            
            # 创建调试图像
            debug_image = self.create_debug_image(image, filtered_masks)
            
            return filtered_masks, debug_image
            
        except Exception as e:
            self.logger.error(f"SAM segmentation failed: {e}")
            return [], image.copy()
    
    def filter_masks(self, masks: List[Dict], image_shape: Tuple[int, int]) -> List[Dict]:
        """过滤掩码以获得种子"""
        if not masks:
            self.logger.warning("No masks to filter")
            self._log_message("没有掩码需要过滤", 'warning')
            return []

        self.logger.info(f"Filtering {len(masks)} masks with parameters:")
        self.logger.info(f"  Area: {self.proc_config.min_seed_area} - {self.proc_config.max_seed_area}")
        self.logger.info(f"  Aspect ratio: {self.proc_config.min_aspect_ratio} - {self.proc_config.max_aspect_ratio}")
        self.logger.info(f"  Solidity: >= {self.proc_config.min_solidity}")

        self._log_message(f"开始过滤 {len(masks)} 个掩码")
        self._log_message(f"过滤参数 - 面积: {self.proc_config.min_seed_area}-{self.proc_config.max_seed_area}, "
                         f"长宽比: {self.proc_config.min_aspect_ratio}-{self.proc_config.max_aspect_ratio}, "
                         f"实心度: >={self.proc_config.min_solidity}")

        filtered_masks = []
        rejection_stats = {
            'area_too_small': 0,
            'area_too_large': 0,
            'aspect_ratio_too_small': 0,
            'aspect_ratio_too_large': 0,
            'solidity_too_low': 0,
            'contour_error': 0
        }

        for i, mask_data in enumerate(masks):
            mask = mask_data['segmentation']

            # 计算掩码属性
            area = mask_data['area']
            bbox = mask_data['bbox']  # [x, y, w, h]

            # 计算纵横比
            aspect_ratio = max(bbox[2], bbox[3]) / min(bbox[2], bbox[3]) if min(bbox[2], bbox[3]) > 0 else float('inf')

            # 计算凸度（solidity）
            try:
                contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if len(contours) > 0:
                    contour = max(contours, key=cv2.contourArea)
                    hull = cv2.convexHull(contour)
                    hull_area = cv2.contourArea(hull)
                    solidity = area / hull_area if hull_area > 0 else 0
                else:
                    solidity = 0
                    self.logger.debug(f"Mask {i}: No contours found")
            except Exception as e:
                solidity = 0
                rejection_stats['contour_error'] += 1
                self.logger.debug(f"Mask {i}: Error computing solidity: {e}")
                continue

            # 检查过滤条件
            rejected = False
            rejection_reasons = []

            if area < self.proc_config.min_seed_area:
                rejected = True
                rejection_reasons.append(f"area too small ({area} < {self.proc_config.min_seed_area})")
                rejection_stats['area_too_small'] += 1
            elif area > self.proc_config.max_seed_area:
                rejected = True
                rejection_reasons.append(f"area too large ({area} > {self.proc_config.max_seed_area})")
                rejection_stats['area_too_large'] += 1

            if aspect_ratio < self.proc_config.min_aspect_ratio:
                rejected = True
                rejection_reasons.append(f"aspect ratio too small ({aspect_ratio:.2f} < {self.proc_config.min_aspect_ratio})")
                rejection_stats['aspect_ratio_too_small'] += 1
            elif aspect_ratio > self.proc_config.max_aspect_ratio:
                rejected = True
                rejection_reasons.append(f"aspect ratio too large ({aspect_ratio:.2f} > {self.proc_config.max_aspect_ratio})")
                rejection_stats['aspect_ratio_too_large'] += 1

            if solidity < self.proc_config.min_solidity:
                rejected = True
                rejection_reasons.append(f"solidity too low ({solidity:.2f} < {self.proc_config.min_solidity})")
                rejection_stats['solidity_too_low'] += 1

            if not rejected:
                # 添加额外信息
                mask_data['aspect_ratio'] = aspect_ratio
                mask_data['solidity'] = solidity
                mask_data['contour'] = contour

                filtered_masks.append(mask_data)

                self.logger.debug(f"✓ Accepted mask {i}: area={area}, aspect_ratio={aspect_ratio:.2f}, solidity={solidity:.2f}")
            else:
                self.logger.debug(f"✗ Rejected mask {i}: {'; '.join(rejection_reasons)}")

        # 按面积排序（大的在前）
        filtered_masks.sort(key=lambda x: x['area'], reverse=True)

        # 记录过滤统计
        self.logger.info(f"Filtering completed: {len(filtered_masks)}/{len(masks)} masks passed")
        self._log_message(f"过滤完成: {len(filtered_masks)}/{len(masks)} 个掩码通过")

        if len(filtered_masks) == 0:
            self.logger.warning("No masks passed filtering. Rejection statistics:")
            self._log_message("没有掩码通过过滤。拒绝统计:", 'warning')
            for reason, count in rejection_stats.items():
                if count > 0:
                    self.logger.warning(f"  {reason}: {count}")
                    self._log_message(f"  {reason}: {count}", 'warning')

        return filtered_masks
    
    def create_debug_image(self, image: np.ndarray, masks: List[Dict]) -> np.ndarray:
        """创建调试图像"""
        debug_image = image.copy()
        
        if len(masks) == 0:
            # 没有检测到种子
            cv2.putText(debug_image, "No seeds detected with SAM", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
            return debug_image
        
        # 绘制检测到的种子
        for i, mask_data in enumerate(masks):
            mask = mask_data['segmentation']
            bbox = mask_data['bbox']
            
            # 创建彩色掩码
            color = np.random.randint(0, 255, 3).tolist()
            colored_mask = np.zeros_like(image)
            colored_mask[mask] = color
            
            # 叠加掩码
            debug_image = cv2.addWeighted(debug_image, 0.8, colored_mask, 0.2, 0)
            
            # 绘制边界框
            x, y, w, h = bbox
            cv2.rectangle(debug_image, (int(x), int(y)), (int(x + w), int(y + h)), color, 2)
            
            # 添加标签
            label = f"Seed {i+1}"
            cv2.putText(debug_image, label, (int(x), int(y) - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # 添加总数信息
        cv2.putText(debug_image, f"SAM detected: {len(masks)} seeds", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        return debug_image
    
    def extract_seed_crops(self, image: np.ndarray, masks: List[Dict], base_filename: str, species_id: str) -> List[str]:
        """提取种子裁剪图像"""
        crops_dir = Path(self.proc_config.output_directory) / "crops" / f"species_{species_id}"
        crops_dir.mkdir(parents=True, exist_ok=True)
        
        saved_files = []
        
        for i, mask_data in enumerate(masks):
            mask = mask_data['segmentation']
            bbox = mask_data['bbox']
            
            # 扩展边界框以包含更多上下文
            x, y, w, h = bbox
            padding = 20
            x1 = max(0, int(x - padding))
            y1 = max(0, int(y - padding))
            x2 = min(image.shape[1], int(x + w + padding))
            y2 = min(image.shape[0], int(y + h + padding))
            
            # 提取裁剪区域
            crop_image = image[y1:y2, x1:x2].copy()
            crop_mask = mask[y1:y2, x1:x2]
            
            # 应用掩码（可选：将背景设为白色）
            if self.proc_config.save_debug_images:
                # 保留原始裁剪
                crop_filename = f"{base_filename}_seed_{i:03d}.jpg"
            else:
                # 应用掩码，背景变白
                crop_image[~crop_mask] = [255, 255, 255]
                crop_filename = f"{base_filename}_seed_{i:03d}_masked.jpg"
            
            crop_path = crops_dir / crop_filename
            
            # 保存裁剪图像
            success = cv2.imwrite(str(crop_path), crop_image)
            if success:
                saved_files.append(str(crop_path))
                self.logger.info(f"Saved seed crop: {crop_path}")
            else:
                self.logger.error(f"Failed to save crop: {crop_path}")
        
        return saved_files
    
    def create_yolo_annotations(self, masks: List[Dict], image_shape: Tuple[int, int], species_id: str) -> str:
        """创建YOLO格式的注释"""
        if not self.proc_config.create_yolo_annotations:
            return ""
        
        height, width = image_shape[:2]
        annotations = []
        
        # 假设所有种子属于同一类别（可以根据species_id映射）
        class_id = 0  # 可以根据需要修改
        
        for mask_data in masks:
            bbox = mask_data['bbox']
            x, y, w, h = bbox
            
            # 转换为YOLO格式（归一化的中心坐标和宽高）
            center_x = (x + w / 2) / width
            center_y = (y + h / 2) / height
            norm_width = w / width
            norm_height = h / height
            
            annotations.append(f"{class_id} {center_x:.6f} {center_y:.6f} {norm_width:.6f} {norm_height:.6f}")
        
        return "\n".join(annotations)
    
    def process_single_image(self, image_path: str) -> Dict:
        """处理单个图像"""
        try:
            filename = Path(image_path).name
            base_filename = Path(image_path).stem
            species_id = self.extract_species_id(filename)

            self.logger.info(f"Processing {filename} (Species: {species_id})")
            self._log_message(f"开始处理图像: {filename}")

            # 加载图像
            self.logger.info(f"Loading image: {image_path}")
            image = cv2.imread(image_path)
            if image is None:
                error_msg = f"Cannot load image: {image_path}"
                self.logger.error(error_msg)
                self._log_message(f"图像加载失败: {filename}", 'error')
                raise ValueError(error_msg)

            h, w, c = image.shape
            self.logger.info(f"Image loaded successfully: {w}x{h}x{c}")
            self._log_message(f"图像加载成功: {w}x{h} 像素")

            # 检查图像大小
            if w * h > 20000000:  # 20M pixels
                self.logger.warning(f"Large image detected: {w}x{h}, may cause memory issues")
                self._log_message(f"图像很大 ({w}x{h})，可能导致内存问题", 'warning')

            # 使用SAM进行分割
            self.logger.info("Starting SAM segmentation...")
            self._log_message("开始SAM分割...")
            masks, debug_image = self.segment_seeds_with_sam(image)

            self.logger.info(f"SAM segmentation completed, found {len(masks)} valid masks")
            self._log_message(f"SAM分割完成，找到 {len(masks)} 个有效掩码")

            if len(masks) == 0:
                self.logger.warning(f"No seeds found in {filename}")
                self._log_message(f"在 {filename} 中未找到种子", 'warning')

                # 仍然保存调试图像以便分析
                debug_image_path = None
                if debug_image is not None and self.proc_config.save_debug_images:
                    debug_dir = Path(self.proc_config.output_directory) / "debug"
                    debug_dir.mkdir(parents=True, exist_ok=True)
                    debug_path = debug_dir / f"sam_debug_{filename}"
                    cv2.imwrite(str(debug_path), debug_image)
                    debug_image_path = str(debug_path)
                    self.logger.info(f"Saved debug image (no seeds): {debug_path}")
                    self._log_message(f"已保存调试图像: {debug_path.name}")

                return {
                    'filename': filename,
                    'species_id': species_id,
                    'seeds_found': 0,
                    'success': True,
                    'saved_files': [],
                    'yolo_annotation': "",
                    'debug_image_path': debug_image_path
                }

            # 提取种子裁剪
            self.logger.info("Extracting seed crops...")
            self._log_message("提取种子裁剪图像...")
            saved_files = self.extract_seed_crops(image, masks, base_filename, species_id)

            # 创建YOLO注释
            self.logger.info("Creating YOLO annotations...")
            yolo_annotation = self.create_yolo_annotations(masks, image.shape, species_id)

            # 保存YOLO注释文件
            if yolo_annotation:
                ann_dir = Path(self.proc_config.output_directory) / "annotations"
                ann_dir.mkdir(parents=True, exist_ok=True)
                ann_path = ann_dir / f"{base_filename}.txt"
                with open(ann_path, 'w') as f:
                    f.write(yolo_annotation)
                self.logger.info(f"Saved YOLO annotation: {ann_path}")
                self._log_message(f"已保存YOLO注释: {ann_path.name}")

            # 保存调试图像
            debug_image_path = None
            if debug_image is not None and self.proc_config.save_debug_images:
                debug_dir = Path(self.proc_config.output_directory) / "debug"
                debug_dir.mkdir(parents=True, exist_ok=True)
                debug_path = debug_dir / f"sam_debug_{filename}"
                cv2.imwrite(str(debug_path), debug_image)
                debug_image_path = str(debug_path)
                self.logger.info(f"Saved debug image: {debug_path}")
                self._log_message(f"已保存调试图像: {debug_path.name}")

            # 更新统计
            self.processing_stats['total_seeds_found'] += len(masks)
            if species_id not in self.processing_stats['species_counts']:
                self.processing_stats['species_counts'][species_id] = 0
            self.processing_stats['species_counts'][species_id] += len(masks)

            self.logger.info(f"Successfully processed {filename}: {len(masks)} seeds found")
            self._log_message(f"成功处理 {filename}: 找到 {len(masks)} 个种子")

            return {
                'filename': filename,
                'species_id': species_id,
                'seeds_found': len(masks),
                'success': True,
                'saved_files': saved_files,
                'yolo_annotation': yolo_annotation,
                'debug_image_path': debug_image_path
            }

        except Exception as e:
            error_msg = f"Error processing {image_path}: {e}"
            self.logger.error(error_msg)
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            self._log_message(f"处理 {Path(image_path).name} 时出错: {e}", 'error')

            return {
                'filename': Path(image_path).name,
                'species_id': 'unknown',
                'seeds_found': 0,
                'success': False,
                'error': str(e),
                'saved_files': [],
                'yolo_annotation': "",
                'debug_image_path': None
            }
    
    def process_directory(self) -> Dict:
        """处理整个目录"""
        input_dir = Path(self.proc_config.input_directory)
        if not input_dir.exists():
            raise FileNotFoundError(f"Input directory not found: {input_dir}")
        
        # 获取图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_dir.glob(f"*{ext}"))
            image_files.extend(input_dir.glob(f"*{ext.upper()}"))
        
        if not image_files:
            raise ValueError(f"No image files found in {input_dir}")
        
        # 预览模式限制
        if self.proc_config.preview_mode:
            image_files = image_files[:self.proc_config.max_preview_images]
            self.logger.info(f"Preview mode: processing {len(image_files)} images")
        
        self.logger.info(f"Found {len(image_files)} images to process")
        
        # 处理图像
        results = []
        start_time = datetime.now()

        self.start_processing()

        for i, image_path in enumerate(image_files, 1):
            # 检查是否需要停止
            if self.should_stop:
                self._log_message(f"Processing stopped by user at {i-1}/{len(image_files)}", 'warning')
                break

            # 检查是否需要暂停
            while self.should_pause and not self.should_stop:
                import time
                time.sleep(0.1)

            # 更新进度
            self._update_progress(i-1, len(image_files), image_path.name, 0)
            self._log_message(f"Processing {i}/{len(image_files)}: {image_path.name}")

            result = self.process_single_image(str(image_path))
            results.append(result)

            # 更新统计
            self.processing_stats['processed_images'] = i
            self.processing_stats['current_image'] = image_path.name

            if result['success'] and result['seeds_found'] > 0:
                self._log_message(f"✓ Found {result['seeds_found']} seeds in {result['filename']}")
                self._update_progress(i, len(image_files), image_path.name, result['seeds_found'])
            elif result['success']:
                self._log_message(f"⚠ No seeds found in {result['filename']}", 'warning')
                self._update_progress(i, len(image_files), image_path.name, 0)
            else:
                self._log_message(f"✗ Failed to process {result['filename']}", 'error')
                self._update_progress(i, len(image_files), image_path.name, 0)

        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        self.is_processing = False
        
        # 更新统计
        self.processing_stats['total_images'] = len(image_files)
        self.processing_stats['processing_time'] = processing_time
        
        # 保存处理报告
        self.save_processing_report(results)
        
        return {
            'total_images': len(image_files),
            'successful_images': sum(1 for r in results if r['success']),
            'total_seeds_found': self.processing_stats['total_seeds_found'],
            'processing_time': processing_time,
            'results': results
        }
    
    def save_processing_report(self, results: List[Dict]):
        """保存处理报告"""
        output_dir = Path(self.proc_config.output_directory)
        
        # 保存详细报告
        report_path = output_dir / "sam_processing_report.json"
        report_data = {
            'processing_stats': self.processing_stats,
            'sam_config': {
                'model_type': self.sam_config.model_type,
                'checkpoint_path': self.sam_config.checkpoint_path,
                'points_per_side': self.sam_config.points_per_side,
                'pred_iou_thresh': self.sam_config.pred_iou_thresh,
                'stability_score_thresh': self.sam_config.stability_score_thresh,
                'min_mask_region_area': self.sam_config.min_mask_region_area
            },
            'processing_config': {
                'min_seed_area': self.proc_config.min_seed_area,
                'max_seed_area': self.proc_config.max_seed_area,
                'min_aspect_ratio': self.proc_config.min_aspect_ratio,
                'max_aspect_ratio': self.proc_config.max_aspect_ratio,
                'min_solidity': self.proc_config.min_solidity
            },
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Processing report saved: {report_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SAM-based Seed Segmentation Tool")
    parser.add_argument("input_dir", help="Input directory containing seed images")
    parser.add_argument("output_dir", help="Output directory for results")
    parser.add_argument("--model-path", default="sam_vit_h_4b8939.pth", help="Path to SAM model checkpoint")
    parser.add_argument("--preview", action="store_true", help="Preview mode (process only first few images)")
    parser.add_argument("--max-preview", type=int, default=5, help="Maximum images in preview mode")
    parser.add_argument("--device", choices=["auto", "cpu", "cuda"], default="auto", help="Device to use")
    parser.add_argument("--min-area", type=int, default=1000, help="Minimum seed area")
    parser.add_argument("--max-area", type=int, default=500000, help="Maximum seed area")
    
    args = parser.parse_args()
    
    if not SAM_AVAILABLE:
        print("❌ Segment Anything not available. Please install with:")
        print("pip install segment-anything")
        return 1
    
    # 配置
    sam_config = SAMConfig(
        checkpoint_path=args.model_path,
        device=args.device
    )
    
    processing_config = ProcessingConfig(
        input_directory=args.input_dir,
        output_directory=args.output_dir,
        preview_mode=args.preview,
        max_preview_images=args.max_preview,
        min_seed_area=args.min_area,
        max_seed_area=args.max_area
    )
    
    try:
        # 创建分割器
        segmenter = SAMSeedSegmenter(sam_config, processing_config)
        
        # 处理图像
        results = segmenter.process_directory()
        
        # 显示结果
        print("\n" + "="*60)
        print("🎉 SAM种子分割完成!")
        print("🎉 SAM Seed Segmentation Complete!")
        print("="*60)
        print(f"📊 处理图像: {results['total_images']}")
        print(f"📊 Images processed: {results['total_images']}")
        print(f"✅ 成功处理: {results['successful_images']}")
        print(f"✅ Successfully processed: {results['successful_images']}")
        print(f"🌱 检测到种子: {results['total_seeds_found']}")
        print(f"🌱 Seeds detected: {results['total_seeds_found']}")
        print(f"⏱️ 处理时间: {results['processing_time']:.1f}秒")
        print(f"⏱️ Processing time: {results['processing_time']:.1f} seconds")
        print(f"📁 输出目录: {args.output_dir}")
        print(f"📁 Output directory: {args.output_dir}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
