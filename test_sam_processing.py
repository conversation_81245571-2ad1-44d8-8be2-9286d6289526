#!/usr/bin/env python3
"""
Test SAM processing with actual image
"""

import sys
import os
from pathlib import Path
import shutil

def main():
    print("Testing SAM processing with actual image...")
    
    try:
        from sam_seed_segmenter import SAMSeedSegmenter, SAMConfig, ProcessingConfig
        print("✓ SAM backend import successful")
    except ImportError as e:
        print(f"✗ SAM backend import failed: {e}")
        return 1
    
    # Create test output directory
    test_output_dir = Path("test_output")
    if test_output_dir.exists():
        shutil.rmtree(test_output_dir)
    test_output_dir.mkdir()
    
    # Configuration
    sam_config = SAMConfig(
        checkpoint_path="sam_vit_h_4b8939.pth",
        device="cpu"
    )
    
    processing_config = ProcessingConfig(
        input_directory="CVH-seed-pic",
        output_directory=str(test_output_dir),
        preview_mode=True,
        max_preview_images=1,  # Only process 1 image for testing
        save_debug_images=True,
        create_yolo_annotations=True,
        min_seed_area=100,
        max_seed_area=50000,
        min_aspect_ratio=0.3,
        max_aspect_ratio=3.0,
        min_solidity=0.8
    )
    
    try:
        # Initialize segmenter
        print("Initializing SAM segmenter...")
        segmenter = SAMSeedSegmenter(sam_config, processing_config)
        print("✓ SAM segmenter initialized")
        
        # Find a test image
        input_path = Path("CVH-seed-pic")
        image_files = list(input_path.glob("*.jpg"))[:1]  # Just one image
        
        if not image_files:
            print("✗ No test images found")
            return 1
        
        test_image = image_files[0]
        print(f"Processing test image: {test_image.name}")
        
        # Process the image
        result = segmenter.process_single_image(str(test_image))
        
        if result and result.get('success', False):
            seeds_found = result.get('seeds_found', 0)
            print(f"✓ Processing successful: {seeds_found} seeds detected")
            
            # Check output files
            crops_dir = test_output_dir / "crops"
            debug_dir = test_output_dir / "debug"
            ann_dir = test_output_dir / "annotations"
            
            if crops_dir.exists():
                crop_files = list(crops_dir.rglob("*.jpg"))
                print(f"✓ Found {len(crop_files)} crop files")
            else:
                print("✗ No crops directory created")
            
            if debug_dir.exists():
                debug_files = list(debug_dir.glob("*.jpg"))
                print(f"✓ Found {len(debug_files)} debug files")
            else:
                print("✗ No debug directory created")
            
            if ann_dir.exists():
                ann_files = list(ann_dir.glob("*.txt"))
                print(f"✓ Found {len(ann_files)} annotation files")
            else:
                print("✗ No annotations directory created")
            
            print(f"\n✓ Test completed successfully!")
            print(f"Output directory: {test_output_dir.absolute()}")
            
        else:
            print("✗ Processing failed")
            return 1
            
    except Exception as e:
        print(f"✗ Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
