#!/usr/bin/env python3
"""
快速调试测试脚本
用于快速诊断SAM处理问题
"""

import sys
import os
from pathlib import Path

def main():
    """主函数"""
    print("🔍 快速SAM调试测试")
    print("=" * 40)
    
    # 检查基本文件
    config_file = "sam_gui_config.json"
    model_path = "sam_vit_h_4b8939.pth"
    input_dir = "input_images"
    
    if Path(config_file).exists():
        import json
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            model_path = config.get('model_path', model_path)
            input_dir = config.get('input_dir', input_dir)
            print(f"✅ 配置文件加载成功")
        except Exception as e:
            print(f"⚠️ 配置文件读取失败: {e}")
    
    print(f"📁 输入目录: {input_dir}")
    print(f"🤖 SAM模型: {model_path}")
    
    # 检查输入目录
    if not Path(input_dir).exists():
        print(f"❌ 输入目录不存在: {input_dir}")
        print("请创建输入目录并放入一些测试图像")
        return 1
    
    # 检查图像文件
    input_path = Path(input_dir)
    image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))
    if not image_files:
        print(f"❌ 输入目录中没有图像文件")
        print("请在输入目录中放入一些.jpg或.png图像文件")
        return 1
    
    print(f"📷 找到 {len(image_files)} 个图像文件")
    
    # 检查SAM模型
    if not Path(model_path).exists():
        print(f"❌ SAM模型文件不存在: {model_path}")
        print("请下载SAM模型文件:")
        print("wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth")
        return 1
    
    print(f"✅ SAM模型文件存在")
    
    # 运行详细调试
    print("\n🧪 运行详细调试...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "debug_sam_detailed.py"], 
                              capture_output=True, text=True, timeout=300)
        
        print("调试输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 详细调试完成")
        else:
            print(f"❌ 详细调试失败 (返回码: {result.returncode})")
            
    except subprocess.TimeoutExpired:
        print("⏰ 调试超时（5分钟）")
    except FileNotFoundError:
        print("❌ 找不到详细调试脚本 debug_sam_detailed.py")
        print("请确保该文件存在")
    except Exception as e:
        print(f"❌ 运行详细调试失败: {e}")
    
    # 提供建议
    print("\n💡 建议:")
    print("1. 运行详细调试: python debug_sam_detailed.py")
    print("2. 运行完整诊断: python diagnose_sam_processing.py")
    print("3. 检查GUI日志中的详细错误信息")
    print("4. 尝试调整SAM参数:")
    print("   - 降低pred_iou_thresh (0.7 -> 0.5)")
    print("   - 降低stability_score_thresh (0.95 -> 0.8)")
    print("   - 增加points_per_side (32 -> 64)")
    print("   - 降低过滤参数 (min_seed_area, min_solidity等)")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
