#!/usr/bin/env python3
"""
YOLOE GUI测试脚本
验证YOLOE GUI的各项功能，确保与CVH-seed-pic数据集兼容
"""

import sys
import os
import json
import shutil
from pathlib import Path
import time

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from yoloe_seed_detector import YOLOESeedDetector, YOLOEConfig, ProcessingConfig, YOLOE_AVAILABLE
        print("✅ YOLOE检测器导入成功")
        print(f"   YOLOE可用: {YOLOE_AVAILABLE}")
    except ImportError as e:
        print(f"❌ YOLOE检测器导入失败: {e}")
        return False
    
    try:
        from yoloe_gui_advanced import YOLOEGUIAdvanced, SessionManager, ChineseTextManager
        print("✅ YOLOE GUI导入成功")
    except ImportError as e:
        print(f"❌ YOLOE GUI导入失败: {e}")
        return False
    
    try:
        from yolo_training_module import YOLOTrainingManager, TRAINING_AVAILABLE
        print("✅ YOLO训练模块导入成功")
        print(f"   训练功能可用: {TRAINING_AVAILABLE}")
    except ImportError as e:
        print(f"❌ YOLO训练模块导入失败: {e}")
        return False
    
    return True

def test_config_file():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    config_file = "yoloe_gui_config.json"
    if not Path(config_file).exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件加载成功")
        
        # 检查必需的配置项
        required_sections = ['app_info', 'default_settings', 'yoloe_models', 'parameter_presets']
        for section in required_sections:
            if section in config:
                print(f"✅ 配置节存在: {section}")
            else:
                print(f"❌ 配置节缺失: {section}")
                return False
        
        # 检查默认设置
        default_settings = config.get('default_settings', {})
        required_settings = ['input_dir', 'output_dir', 'model_path', 'confidence']
        for setting in required_settings:
            if setting in default_settings:
                print(f"✅ 默认设置存在: {setting} = {default_settings[setting]}")
            else:
                print(f"❌ 默认设置缺失: {setting}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_session_manager():
    """测试会话管理器"""
    print("\n测试会话管理器...")
    
    try:
        from yoloe_gui_advanced import SessionManager
        
        # 创建测试会话管理器
        test_output_dir = "test_yoloe_output"
        session_mgr = SessionManager(test_output_dir)
        
        # 测试创建新会话
        session_id, session_dir = session_mgr.create_new_session()
        print(f"✅ 创建会话成功: {session_id}")
        print(f"   会话目录: {session_dir}")
        
        # 检查目录结构
        expected_dirs = ['crops', 'annotations', 'debug', 'models', 'logs']
        for dir_name in expected_dirs:
            dir_path = session_dir / dir_name
            if dir_path.exists():
                print(f"✅ 子目录存在: {dir_name}")
            else:
                print(f"❌ 子目录缺失: {dir_name}")
                return False
        
        # 测试会话列表
        sessions = session_mgr.get_session_list()
        print(f"✅ 获取会话列表: {len(sessions)} 个会话")
        
        # 清理测试目录
        if Path(test_output_dir).exists():
            shutil.rmtree(test_output_dir)
            print("✅ 清理测试目录")
        
        return True
        
    except Exception as e:
        print(f"❌ 会话管理器测试失败: {e}")
        return False

def test_yoloe_config():
    """测试YOLOE配置"""
    print("\n测试YOLOE配置...")
    
    try:
        from yoloe_seed_detector import YOLOEConfig, ProcessingConfig
        
        # 测试YOLOE配置
        yoloe_config = YOLOEConfig(
            model_path="yolov8n.pt",
            device="cpu",
            confidence=0.25,
            iou_threshold=0.45,
            image_size=640
        )
        print("✅ YOLOE配置创建成功")
        print(f"   模型路径: {yoloe_config.model_path}")
        print(f"   设备: {yoloe_config.device}")
        print(f"   置信度: {yoloe_config.confidence}")
        
        # 测试处理配置
        processing_config = ProcessingConfig(
            input_directory="CVH-seed-pic",
            output_directory="test_output",
            preview_mode=True,
            max_preview_images=5,
            min_seed_area=100,
            max_seed_area=50000
        )
        print("✅ 处理配置创建成功")
        print(f"   输入目录: {processing_config.input_directory}")
        print(f"   输出目录: {processing_config.output_directory}")
        print(f"   预览模式: {processing_config.preview_mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ YOLOE配置测试失败: {e}")
        return False

def test_yoloe_detector():
    """测试YOLOE检测器"""
    print("\n测试YOLOE检测器...")
    
    try:
        from yoloe_seed_detector import YOLOESeedDetector, YOLOEConfig, ProcessingConfig, YOLOE_AVAILABLE
        
        if not YOLOE_AVAILABLE:
            print("⚠️ YOLOE不可用，跳过检测器测试")
            return True
        
        # 检查输入目录
        input_dir = "CVH-seed-pic"
        if not Path(input_dir).exists():
            print(f"⚠️ 输入目录不存在: {input_dir}，跳过检测器测试")
            return True
        
        # 检查模型文件
        model_path = "yolov8n.pt"
        if not Path(model_path).exists():
            print(f"⚠️ 模型文件不存在: {model_path}，跳过检测器测试")
            return True
        
        # 创建配置
        yoloe_config = YOLOEConfig(
            model_path=model_path,
            device="cpu",
            confidence=0.25
        )
        
        processing_config = ProcessingConfig(
            input_directory=input_dir,
            output_directory="test_yoloe_detector_output",
            preview_mode=True,
            max_preview_images=1
        )
        
        # 创建检测器
        detector = YOLOESeedDetector(yoloe_config, processing_config)
        print("✅ YOLOE检测器创建成功")
        
        # 测试单个图像处理
        image_files = list(Path(input_dir).glob("*.jpg"))[:1]
        if image_files:
            test_image = image_files[0]
            print(f"测试图像: {test_image.name}")
            
            result = detector.process_single_image(str(test_image))
            
            if result and result.get('success', False):
                seeds_found = result.get('seeds_found', 0)
                print(f"✅ 图像处理成功: 检测到 {seeds_found} 个种子")
                
                # 检查输出文件
                output_dir = Path("test_yoloe_detector_output")
                if output_dir.exists():
                    crops_dir = output_dir / "crops"
                    debug_dir = output_dir / "debug"
                    ann_dir = output_dir / "annotations"
                    
                    if crops_dir.exists():
                        crop_files = list(crops_dir.rglob("*.jpg"))
                        print(f"✅ 生成裁剪图像: {len(crop_files)} 个")
                    
                    if debug_dir.exists():
                        debug_files = list(debug_dir.glob("*.jpg"))
                        print(f"✅ 生成调试图像: {len(debug_files)} 个")
                    
                    if ann_dir.exists():
                        ann_files = list(ann_dir.glob("*.txt"))
                        print(f"✅ 生成注释文件: {len(ann_files)} 个")
                    
                    # 清理测试输出
                    shutil.rmtree(output_dir)
                    print("✅ 清理测试输出")
            else:
                print("⚠️ 图像处理完成但未检测到种子")
        else:
            print("⚠️ 未找到测试图像")
        
        return True
        
    except Exception as e:
        print(f"❌ YOLOE检测器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n测试GUI组件...")
    
    try:
        import tkinter as tk
        from yoloe_gui_advanced import ChineseTextManager
        
        # 测试中文文本管理器
        text_mgr = ChineseTextManager()
        print("✅ 中文文本管理器创建成功")
        
        # 测试一些文本
        test_keys = ['app_title', 'file_settings', 'parameters', 'processing_control']
        for key in test_keys:
            text = text_mgr.get_text(key)
            print(f"✅ 文本获取: {key} = {text}")
        
        # 测试tkinter可用性
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        print("✅ Tkinter可用")
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件测试失败: {e}")
        return False

def test_training_module():
    """测试训练模块"""
    print("\n测试训练模块...")
    
    try:
        from yolo_training_module import YOLOTrainingManager, TRAINING_AVAILABLE
        
        if not TRAINING_AVAILABLE:
            print("⚠️ 训练功能不可用，跳过训练模块测试")
            return True
        
        # 创建训练管理器
        trainer = YOLOTrainingManager("test_training_output")
        print("✅ 训练管理器创建成功")
        
        # 测试统计信息
        stats = trainer.get_training_stats()
        print(f"✅ 获取训练统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练模块测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        "yoloe_seed_detector.py",
        "yoloe_gui_advanced.py", 
        "yolo_training_module.py",
        "yoloe_gui_config.json",
        "start_yoloe_gui.bat",
        "start_yoloe_gui.sh",
        "check_yoloe_environment.py"
    ]
    
    all_exist = True
    for file_name in required_files:
        if Path(file_name).exists():
            size = Path(file_name).stat().st_size
            print(f"✅ {file_name} ({size} bytes)")
        else:
            print(f"❌ {file_name} 缺失")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("YOLOE GUI功能测试")
    print("="*60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config_file),
        ("会话管理器", test_session_manager),
        ("YOLOE配置", test_yoloe_config),
        ("YOLOE检测器", test_yoloe_detector),
        ("GUI组件", test_gui_components),
        ("训练模块", test_training_module),
        ("文件结构", test_file_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 总结
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    print(f"通过: {passed}/{total} 项测试")
    
    if passed == total:
        print("\n🎉 所有测试通过！YOLOE GUI可以正常使用")
        print("\n启动方式:")
        print("1. 双击 start_yoloe_gui.bat (Windows)")
        print("2. 运行 ./start_yoloe_gui.sh (Linux/Mac)")
        print("3. 直接运行 python yoloe_gui_advanced.py")
    elif passed >= total - 2:
        print("\n⚠️ 大部分测试通过，GUI基本可用")
        print("某些高级功能可能不可用")
    else:
        print("\n❌ 多项测试失败，建议先解决问题")
        print("运行 python check_yoloe_environment.py 检查环境")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
