#!/usr/bin/env python3
"""
YOLOE环境检查脚本
检查YOLOE GUI运行所需的所有依赖项
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_package(package_name, import_name=None):
    """检查Python包是否安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装")
        return False

def check_conda_environment():
    """检查conda环境"""
    print("\n检查conda环境...")
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ 当前conda环境: {conda_env}")
        if conda_env == 'cv':
            print("✅ 正在使用推荐的'cv'环境")
            return True
        else:
            print("⚠️ 建议使用'cv'环境")
            return True
    else:
        print("❌ 未检测到conda环境")
        return False

def check_gpu_support():
    """检查GPU支持"""
    print("\n检查GPU支持...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            print(f"✅ 检测到 {gpu_count} 个GPU: {gpu_name}")
            print(f"✅ CUDA版本: {torch.version.cuda}")
            return True
        else:
            print("⚠️ 未检测到可用GPU，将使用CPU")
            return False
    except ImportError:
        print("❌ PyTorch未安装，无法检查GPU")
        return False

def check_required_files():
    """检查必需文件"""
    print("\n检查必需文件...")
    
    required_files = [
        "yoloe_seed_detector.py",
        "yoloe_gui_advanced.py",
        "yolo_training_module.py",
        "yoloe_gui_config.json"
    ]
    
    all_exist = True
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} 缺失")
            all_exist = False
    
    return all_exist

def check_input_directory():
    """检查输入目录"""
    print("\n检查输入目录...")
    
    input_dir = Path("CVH-seed-pic")
    if input_dir.exists():
        image_files = list(input_dir.glob("*.jpg")) + list(input_dir.glob("*.png"))
        print(f"✅ 输入目录存在: {input_dir}")
        print(f"✅ 找到 {len(image_files)} 个图像文件")
        return True
    else:
        print(f"❌ 输入目录不存在: {input_dir}")
        return False

def check_model_files():
    """检查模型文件"""
    print("\n检查模型文件...")
    
    model_files = ["yolov8n.pt", "yolov8s.pt", "yolov8m.pt"]
    found_models = []
    
    for model_file in model_files:
        if Path(model_file).exists():
            size = Path(model_file).stat().st_size / (1024 * 1024)  # MB
            print(f"✅ {model_file} ({size:.1f}MB)")
            found_models.append(model_file)
        else:
            print(f"⚠️ {model_file} 未找到")
    
    if found_models:
        print(f"✅ 找到 {len(found_models)} 个模型文件")
        return True
    else:
        print("❌ 未找到任何YOLO模型文件")
        print("建议下载: yolov8n.pt (最小模型，适合测试)")
        return False

def provide_installation_guide():
    """提供安装指南"""
    print("\n" + "="*60)
    print("安装指南 / Installation Guide")
    print("="*60)
    
    print("\n1. 安装conda环境:")
    print("   conda create -n cv python=3.9")
    print("   conda activate cv")
    
    print("\n2. 安装PyTorch (CPU版本):")
    print("   pip install torch torchvision torchaudio")
    
    print("\n3. 安装PyTorch (GPU版本，如果有NVIDIA GPU):")
    print("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
    
    print("\n4. 安装其他依赖:")
    print("   pip install ultralytics opencv-python pillow numpy")
    
    print("\n5. 下载YOLO模型:")
    print("   python -c \"from ultralytics import YOLO; YOLO('yolov8n.pt')\"")
    
    print("\n6. 启动GUI:")
    print("   python yoloe_gui_advanced.py")
    print("   或双击: start_yoloe_gui.bat (Windows)")

def main():
    """主函数"""
    print("YOLOE环境检查工具")
    print("YOLOE Environment Checker")
    print("="*60)
    
    checks = []
    
    # 基础检查
    checks.append(("Python版本", check_python_version()))
    checks.append(("Conda环境", check_conda_environment()))
    
    # 包依赖检查
    print("\n检查Python包...")
    packages = [
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("ultralytics", "ultralytics"),
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("pillow", "PIL"),
        ("tkinter", "tkinter")
    ]
    
    for package_name, import_name in packages:
        checks.append((package_name, check_package(package_name, import_name)))
    
    # 其他检查
    checks.append(("GPU支持", check_gpu_support()))
    checks.append(("必需文件", check_required_files()))
    checks.append(("输入目录", check_input_directory()))
    checks.append(("模型文件", check_model_files()))
    
    # 总结
    print("\n" + "="*60)
    print("检查结果总结")
    print("="*60)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in checks:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name:15} : {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！可以启动YOLOE GUI")
        print("运行: python yoloe_gui_advanced.py")
    elif passed >= total - 2:
        print("\n⚠️ 大部分检查通过，可以尝试启动GUI")
        print("某些功能可能不可用")
    else:
        print("\n❌ 多项检查失败，建议先解决依赖问题")
        provide_installation_guide()
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n检查过程中出现错误: {e}")
        sys.exit(1)
