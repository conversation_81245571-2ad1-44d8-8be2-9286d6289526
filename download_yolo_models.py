#!/usr/bin/env python3
"""
下载YOLO模型脚本
"""

import sys
from pathlib import Path

def download_yolo_models():
    """下载YOLO模型"""
    try:
        from ultralytics import YOLO
        print("开始下载YOLO模型...")
        
        # 下载YOLOv8n模型（最小模型）
        print("下载 yolov8n.pt...")
        model = YOLO('yolov8n.pt')
        print("✅ yolov8n.pt 下载完成")
        
        # 检查文件是否存在
        if Path('yolov8n.pt').exists():
            size = Path('yolov8n.pt').stat().st_size / (1024 * 1024)
            print(f"✅ 模型文件大小: {size:.1f}MB")
        
        print("YOLO模型下载完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请先安装ultralytics: pip install ultralytics")
        return False
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

if __name__ == "__main__":
    success = download_yolo_models()
    sys.exit(0 if success else 1)
