@echo off
chcp 65001
echo ========================================
echo YOLOE种子检测工具启动器
echo YOLOE Seed Detection Tool Launcher
echo ========================================
echo.

echo 正在激活conda环境...
echo Activating conda environment...
call conda activate cv

if %errorlevel% neq 0 (
    echo 错误: 无法激活conda环境 'cv'
    echo Error: Cannot activate conda environment 'cv'
    echo 请确保已安装conda并创建了'cv'环境
    echo Please ensure conda is installed and 'cv' environment exists
    pause
    exit /b 1
)

echo 环境激活成功
echo Environment activated successfully
echo.

echo 检查Python环境...
echo Checking Python environment...
python --version

echo.
echo 启动YOLOE GUI...
echo Starting YOLOE GUI...
python yoloe_gui_advanced.py

if %errorlevel% neq 0 (
    echo.
    echo 错误: GUI启动失败
    echo Error: GUI failed to start
    echo 请检查依赖项是否正确安装
    echo Please check if dependencies are properly installed
    echo.
    echo 建议运行以下命令安装依赖:
    echo Recommended to run the following command to install dependencies:
    echo pip install ultralytics torch torchvision opencv-python
)

echo.
pause
