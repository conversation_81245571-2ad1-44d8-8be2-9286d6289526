#!/usr/bin/env python3
"""
诊断SAM输出文件问题
"""

import sys
import os
import json
from pathlib import Path
import shutil

# 设置Windows控制台编码为UTF-8
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

def check_directory_permissions(path):
    """检查目录权限"""
    try:
        # 尝试创建测试文件
        test_file = Path(path) / "test_write.txt"
        with open(test_file, 'w') as f:
            f.write("test")
        test_file.unlink()  # 删除测试文件
        return True, "可写"
    except PermissionError:
        return False, "权限不足"
    except Exception as e:
        return False, f"错误: {e}"

def main():
    print("=" * 60)
    print("SAM输出文件问题诊断")
    print("=" * 60)
    
    # 1. 检查配置文件
    print("\n1. 检查配置文件:")
    config_file = "sam_gui_config.json"
    if Path(config_file).exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"   [OK] 配置文件存在: {config_file}")
            
            # 显示关键配置
            output_dir = config.get('output_dir', 'output')
            input_dir = config.get('input_dir', 'input_images')
            print(f"   输出目录: {output_dir}")
            print(f"   输入目录: {input_dir}")
            
        except Exception as e:
            print(f"   [ERROR] 配置文件读取失败: {e}")
            return 1
    else:
        print(f"   [ERROR] 配置文件不存在: {config_file}")
        return 1
    
    # 2. 检查输出目录
    print("\n2. 检查输出目录:")
    output_path = Path(output_dir)
    if output_path.exists():
        print(f"   [OK] 输出目录存在: {output_path.absolute()}")
        
        # 检查权限
        can_write, perm_msg = check_directory_permissions(output_path)
        print(f"   权限状态: {perm_msg}")
        
        # 列出子目录
        subdirs = [d for d in output_path.iterdir() if d.is_dir()]
        print(f"   子目录数量: {len(subdirs)}")
        
        if subdirs:
            print("   最近的会话目录:")
            # 按修改时间排序，显示最新的几个
            subdirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            for i, subdir in enumerate(subdirs[:5]):
                mtime = subdir.stat().st_mtime
                import datetime
                mtime_str = datetime.datetime.fromtimestamp(mtime).strftime('%Y-%m-%d %H:%M:%S')
                print(f"     {subdir.name} (修改时间: {mtime_str})")
                
                # 检查最新目录的内容
                if i == 0:
                    print(f"   检查最新目录内容: {subdir.name}")
                    try:
                        contents = list(subdir.iterdir())
                        if contents:
                            for item in contents[:10]:  # 只显示前10个
                                if item.is_file():
                                    size = item.stat().st_size
                                    print(f"     文件: {item.name} ({size} bytes)")
                                else:
                                    print(f"     目录: {item.name}/")
                        else:
                            print("     [WARN] 目录为空")
                    except Exception as e:
                        print(f"     [ERROR] 无法读取目录内容: {e}")
        
    else:
        print(f"   [ERROR] 输出目录不存在: {output_path.absolute()}")
        
        # 尝试创建目录
        try:
            output_path.mkdir(parents=True, exist_ok=True)
            print(f"   [OK] 已创建输出目录")
        except Exception as e:
            print(f"   [ERROR] 无法创建输出目录: {e}")
            return 1
    
    # 3. 检查最近的处理会话
    print("\n3. 检查最近的处理会话:")
    session_dirs = [d for d in output_path.iterdir() if d.is_dir() and d.name.startswith('session_')]
    
    if session_dirs:
        # 按名称排序（包含时间戳）
        session_dirs.sort(key=lambda x: x.name, reverse=True)
        latest_session = session_dirs[0]
        print(f"   最新会话: {latest_session.name}")
        
        # 检查会话目录结构
        expected_subdirs = ['crops', 'annotations', 'debug']
        for subdir_name in expected_subdirs:
            subdir_path = latest_session / subdir_name
            if subdir_path.exists():
                file_count = len(list(subdir_path.glob('*')))
                print(f"   {subdir_name}/: {file_count} 个文件")
                
                # 如果是crops目录，显示一些文件
                if subdir_name == 'crops' and file_count > 0:
                    crop_files = list(subdir_path.glob('*.jpg'))[:5]
                    for crop_file in crop_files:
                        size = crop_file.stat().st_size
                        print(f"     {crop_file.name} ({size} bytes)")
            else:
                print(f"   {subdir_name}/: [MISSING]")
        
        # 检查处理日志
        log_file = latest_session / 'processing.log'
        if log_file.exists():
            print(f"   处理日志: 存在 ({log_file.stat().st_size} bytes)")
            # 显示日志的最后几行
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print("   日志最后几行:")
                        for line in lines[-5:]:
                            print(f"     {line.strip()}")
            except Exception as e:
                print(f"   [ERROR] 无法读取日志: {e}")
        else:
            print(f"   处理日志: [MISSING]")
    else:
        print("   [WARN] 没有找到会话目录")
    
    # 4. 检查磁盘空间
    print("\n4. 检查磁盘空间:")
    try:
        total, used, free = shutil.disk_usage(output_path)
        total_gb = total // (1024**3)
        used_gb = used // (1024**3)
        free_gb = free // (1024**3)
        print(f"   总空间: {total_gb} GB")
        print(f"   已使用: {used_gb} GB")
        print(f"   可用空间: {free_gb} GB")
        
        if free_gb < 1:
            print("   [WARN] 磁盘空间不足!")
        else:
            print("   [OK] 磁盘空间充足")
    except Exception as e:
        print(f"   [ERROR] 无法检查磁盘空间: {e}")
    
    # 5. 建议
    print("\n5. 问题诊断和建议:")
    
    if not session_dirs:
        print("   [ISSUE] 没有找到任何会话目录")
        print("   建议: 检查SAM GUI是否正确设置了输出目录")
    elif session_dirs:
        latest_session = session_dirs[0]
        crops_dir = latest_session / 'crops'
        if not crops_dir.exists():
            print("   [ISSUE] 会话目录存在但没有crops子目录")
            print("   建议: 检查SAM处理过程中是否有错误")
        elif len(list(crops_dir.glob('*'))) == 0:
            print("   [ISSUE] crops目录存在但为空")
            print("   建议: 检查种子检测和保存逻辑")
        else:
            print("   [OK] 找到了输出文件")
    
    print("\n6. 手动测试建议:")
    print("   1. 运行: python sam_gui_advanced.py")
    print("   2. 选择一个测试图像")
    print("   3. 检查GUI日志窗口的详细信息")
    print("   4. 确认输出目录设置正确")
    print("   5. 检查是否有错误消息")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
