#!/usr/bin/env python3
"""
YOLO训练模块
用于训练自定义YOLO模型进行种子检测
"""

import os
import sys
import json
import yaml
import shutil
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import random

# 检查YOLO训练依赖
TRAINING_AVAILABLE = False
try:
    import torch
    import torchvision
    from ultralytics import YOLO
    import cv2
    import numpy as np
    TRAINING_AVAILABLE = True
except ImportError as e:
    print(f"YOLO training dependencies not available: {e}")
    print("Please install: pip install ultralytics torch torchvision opencv-python")

class YOLOTrainingManager:
    """YOLO训练管理器"""
    
    def __init__(self, base_dir="output", log_callback=None, progress_callback=None):
        self.base_dir = Path(base_dir)
        self.log_callback = log_callback
        self.progress_callback = progress_callback
        
        # 训练控制
        self.is_training = False
        self.should_stop = False
        
        # 设置日志
        self.setup_logging()
        
        # 训练统计
        self.training_stats = {
            'total_images': 0,
            'train_images': 0,
            'val_images': 0,
            'classes': [],
            'epochs_completed': 0,
            'best_map': 0.0
        }
    
    def setup_logging(self):
        """设置日志"""
        log_dir = self.base_dir / "training_logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"yolo_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('YOLOTraining')
    
    def _log_message(self, message, level='info'):
        """发送日志消息"""
        if self.log_callback:
            self.log_callback(message, level)
        
        if level == 'info':
            self.logger.info(message)
        elif level == 'warning':
            self.logger.warning(message)
        elif level == 'error':
            self.logger.error(message)
    
    def _update_progress(self, current, total, message=''):
        """更新进度"""
        if self.progress_callback:
            progress_data = {
                'current': current,
                'total': total,
                'percentage': (current / total * 100) if total > 0 else 0,
                'message': message
            }
            self.progress_callback(progress_data)
    
    def prepare_dataset_from_annotations(self, annotations_dir: str, images_dir: str, 
                                       output_dir: str, train_ratio: float = 0.8) -> Dict:
        """从注释文件准备YOLO训练数据集"""
        try:
            self._log_message("开始准备YOLO训练数据集...")
            
            annotations_path = Path(annotations_dir)
            images_path = Path(images_dir)
            output_path = Path(output_dir)
            
            if not annotations_path.exists():
                raise ValueError(f"注释目录不存在: {annotations_dir}")
            
            if not images_path.exists():
                raise ValueError(f"图像目录不存在: {images_dir}")
            
            # 创建输出目录结构
            dataset_dir = output_path / "yolo_dataset"
            train_images_dir = dataset_dir / "images" / "train"
            val_images_dir = dataset_dir / "images" / "val"
            train_labels_dir = dataset_dir / "labels" / "train"
            val_labels_dir = dataset_dir / "labels" / "val"
            
            for dir_path in [train_images_dir, val_images_dir, train_labels_dir, val_labels_dir]:
                dir_path.mkdir(parents=True, exist_ok=True)
            
            # 获取所有注释文件
            annotation_files = list(annotations_path.glob("*.txt"))
            if not annotation_files:
                raise ValueError("注释目录中没有找到.txt文件")
            
            self._log_message(f"找到 {len(annotation_files)} 个注释文件")
            
            # 验证对应的图像文件存在
            valid_pairs = []
            for ann_file in annotation_files:
                # 查找对应的图像文件
                base_name = ann_file.stem
                image_file = None
                for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                    potential_image = images_path / f"{base_name}{ext}"
                    if potential_image.exists():
                        image_file = potential_image
                        break
                
                if image_file:
                    valid_pairs.append((image_file, ann_file))
                else:
                    self._log_message(f"警告: 找不到对应的图像文件: {base_name}", 'warning')
            
            if not valid_pairs:
                raise ValueError("没有找到有效的图像-注释对")
            
            self._log_message(f"找到 {len(valid_pairs)} 个有效的图像-注释对")
            
            # 随机分割训练集和验证集
            random.shuffle(valid_pairs)
            split_idx = int(len(valid_pairs) * train_ratio)
            train_pairs = valid_pairs[:split_idx]
            val_pairs = valid_pairs[split_idx:]
            
            self._log_message(f"训练集: {len(train_pairs)} 个样本")
            self._log_message(f"验证集: {len(val_pairs)} 个样本")
            
            # 复制训练集文件
            self._log_message("复制训练集文件...")
            for i, (image_file, ann_file) in enumerate(train_pairs):
                # 复制图像
                shutil.copy2(image_file, train_images_dir / image_file.name)
                # 复制注释
                shutil.copy2(ann_file, train_labels_dir / ann_file.name)
                
                self._update_progress(i + 1, len(train_pairs), f"复制训练集: {image_file.name}")
            
            # 复制验证集文件
            self._log_message("复制验证集文件...")
            for i, (image_file, ann_file) in enumerate(val_pairs):
                # 复制图像
                shutil.copy2(image_file, val_images_dir / image_file.name)
                # 复制注释
                shutil.copy2(ann_file, val_labels_dir / ann_file.name)
                
                self._update_progress(i + 1, len(val_pairs), f"复制验证集: {image_file.name}")
            
            # 创建数据集配置文件
            dataset_config = {
                'path': str(dataset_dir.absolute()),
                'train': 'images/train',
                'val': 'images/val',
                'nc': 1,  # 类别数量（种子）
                'names': ['seed']  # 类别名称
            }
            
            config_file = dataset_dir / "dataset.yaml"
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(dataset_config, f, default_flow_style=False)
            
            self._log_message(f"数据集配置文件已保存: {config_file}")
            
            # 更新统计信息
            self.training_stats.update({
                'total_images': len(valid_pairs),
                'train_images': len(train_pairs),
                'val_images': len(val_pairs),
                'classes': ['seed']
            })
            
            self._log_message("数据集准备完成")
            
            return {
                'success': True,
                'dataset_dir': str(dataset_dir),
                'config_file': str(config_file),
                'train_images': len(train_pairs),
                'val_images': len(val_pairs),
                'total_images': len(valid_pairs)
            }
            
        except Exception as e:
            self._log_message(f"准备数据集失败: {e}", 'error')
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_dataset(self, dataset_config_file: str) -> Dict:
        """验证数据集"""
        try:
            self._log_message("验证数据集...")
            
            config_path = Path(dataset_config_file)
            if not config_path.exists():
                raise ValueError(f"数据集配置文件不存在: {dataset_config_file}")
            
            # 加载配置
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            dataset_path = Path(config['path'])
            train_images_dir = dataset_path / config['train']
            val_images_dir = dataset_path / config['val']
            train_labels_dir = dataset_path / "labels" / "train"
            val_labels_dir = dataset_path / "labels" / "val"
            
            # 检查目录存在
            for dir_path in [train_images_dir, val_images_dir, train_labels_dir, val_labels_dir]:
                if not dir_path.exists():
                    raise ValueError(f"目录不存在: {dir_path}")
            
            # 统计文件数量
            train_images = list(train_images_dir.glob("*.jpg")) + list(train_images_dir.glob("*.png"))
            val_images = list(val_images_dir.glob("*.jpg")) + list(val_images_dir.glob("*.png"))
            train_labels = list(train_labels_dir.glob("*.txt"))
            val_labels = list(val_labels_dir.glob("*.txt"))
            
            self._log_message(f"训练图像: {len(train_images)}")
            self._log_message(f"训练标签: {len(train_labels)}")
            self._log_message(f"验证图像: {len(val_images)}")
            self._log_message(f"验证标签: {len(val_labels)}")
            
            # 检查图像和标签匹配
            train_mismatches = 0
            val_mismatches = 0
            
            for img_file in train_images:
                label_file = train_labels_dir / f"{img_file.stem}.txt"
                if not label_file.exists():
                    train_mismatches += 1
            
            for img_file in val_images:
                label_file = val_labels_dir / f"{img_file.stem}.txt"
                if not label_file.exists():
                    val_mismatches += 1
            
            if train_mismatches > 0:
                self._log_message(f"训练集中有 {train_mismatches} 个图像缺少对应标签", 'warning')
            
            if val_mismatches > 0:
                self._log_message(f"验证集中有 {val_mismatches} 个图像缺少对应标签", 'warning')
            
            # 验证标签格式
            self._log_message("验证标签格式...")
            invalid_labels = 0
            
            for label_file in train_labels + val_labels:
                try:
                    with open(label_file, 'r') as f:
                        lines = f.readlines()
                    
                    for line in lines:
                        parts = line.strip().split()
                        if len(parts) != 5:
                            invalid_labels += 1
                            break
                        
                        # 检查数值范围
                        class_id = int(parts[0])
                        x, y, w, h = map(float, parts[1:])
                        
                        if not (0 <= x <= 1 and 0 <= y <= 1 and 0 <= w <= 1 and 0 <= h <= 1):
                            invalid_labels += 1
                            break
                            
                except Exception:
                    invalid_labels += 1
            
            if invalid_labels > 0:
                self._log_message(f"发现 {invalid_labels} 个无效标签文件", 'warning')
            
            validation_result = {
                'success': True,
                'train_images': len(train_images),
                'train_labels': len(train_labels),
                'val_images': len(val_images),
                'val_labels': len(val_labels),
                'train_mismatches': train_mismatches,
                'val_mismatches': val_mismatches,
                'invalid_labels': invalid_labels,
                'classes': config.get('names', []),
                'num_classes': config.get('nc', 0)
            }
            
            self._log_message("数据集验证完成")
            return validation_result
            
        except Exception as e:
            self._log_message(f"数据集验证失败: {e}", 'error')
            return {
                'success': False,
                'error': str(e)
            }
    
    def train_model(self, dataset_config_file: str, model_name: str = "yolov8n.pt",
                   epochs: int = 100, batch_size: int = 16, learning_rate: float = 0.01,
                   image_size: int = 640, output_dir: str = None) -> Dict:
        """训练YOLO模型"""
        try:
            if not TRAINING_AVAILABLE:
                raise ImportError("YOLO训练依赖不可用")
            
            self._log_message("开始YOLO模型训练...")
            self.is_training = True
            self.should_stop = False
            
            # 设置输出目录
            if output_dir is None:
                output_dir = self.base_dir / "trained_models" / f"training_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            else:
                output_dir = Path(output_dir)
            
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 加载预训练模型
            self._log_message(f"加载预训练模型: {model_name}")
            model = YOLO(model_name)
            
            # 开始训练
            self._log_message(f"开始训练 - 轮数: {epochs}, 批次大小: {batch_size}, 学习率: {learning_rate}")
            
            results = model.train(
                data=dataset_config_file,
                epochs=epochs,
                batch=batch_size,
                lr0=learning_rate,
                imgsz=image_size,
                project=str(output_dir),
                name="seed_detection",
                save=True,
                save_period=10,  # 每10轮保存一次
                patience=20,  # 早停耐心值
                verbose=True
            )
            
            self._log_message("模型训练完成")
            
            # 保存训练结果信息
            training_info = {
                'model_name': model_name,
                'dataset_config': dataset_config_file,
                'epochs': epochs,
                'batch_size': batch_size,
                'learning_rate': learning_rate,
                'image_size': image_size,
                'output_dir': str(output_dir),
                'training_date': datetime.now().isoformat(),
                'results': str(results) if results else None
            }
            
            info_file = output_dir / "training_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(training_info, f, indent=2, ensure_ascii=False)
            
            self.is_training = False
            
            return {
                'success': True,
                'output_dir': str(output_dir),
                'model_path': str(output_dir / "seed_detection" / "weights" / "best.pt"),
                'training_info': training_info
            }
            
        except Exception as e:
            self._log_message(f"模型训练失败: {e}", 'error')
            self.is_training = False
            return {
                'success': False,
                'error': str(e)
            }
    
    def stop_training(self):
        """停止训练"""
        self.should_stop = True
        self.is_training = False
        self._log_message("训练已停止")
    
    def get_training_stats(self) -> Dict:
        """获取训练统计信息"""
        return self.training_stats.copy()

# 主函数用于测试
if __name__ == "__main__":
    print("Testing YOLO Training Module...")
    
    if not TRAINING_AVAILABLE:
        print("YOLO training dependencies not available.")
        sys.exit(1)
    
    # 创建训练管理器
    trainer = YOLOTrainingManager("test_training_output")
    
    # 测试数据集准备
    result = trainer.prepare_dataset_from_annotations(
        annotations_dir="output/session_20250619_160756/annotations",
        images_dir="CVH-seed-pic",
        output_dir="test_training_output"
    )
    
    print(f"Dataset preparation result: {result}")
    
    if result['success']:
        # 测试数据集验证
        validation_result = trainer.validate_dataset(result['config_file'])
        print(f"Dataset validation result: {validation_result}")
