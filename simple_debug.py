#!/usr/bin/env python3
"""
Simple SAM Debug Script
"""

import sys
import os
from pathlib import Path

def main():
    print("SAM Debug Test")
    print("=" * 40)
    
    # Check basic files
    config_file = "sam_gui_config.json"
    model_path = "sam_vit_h_4b8939.pth"
    input_dir = "CVH-seed-pic"
    
    print(f"Input directory: {input_dir}")
    print(f"SAM model: {model_path}")
    
    # Check input directory
    if not Path(input_dir).exists():
        print(f"ERROR: Input directory does not exist: {input_dir}")
        return 1
    
    # Check image files
    input_path = Path(input_dir)
    image_files = list(input_path.glob("*.jpg")) + list(input_path.glob("*.png"))
    if not image_files:
        print(f"ERROR: No image files in input directory")
        return 1
    
    print(f"Found {len(image_files)} image files")
    
    # Check SAM model
    if not Path(model_path).exists():
        print(f"ERROR: SAM model file does not exist: {model_path}")
        return 1
    
    print(f"SAM model file exists")
    
    # Test SAM import
    try:
        from segment_anything import sam_model_registry, SamAutomaticMaskGenerator
        print("SAM import successful")
    except ImportError as e:
        print(f"ERROR: Cannot import SAM: {e}")
        return 1
    
    # Test image loading
    try:
        import cv2
        test_image_path = image_files[0]
        image = cv2.imread(str(test_image_path))
        if image is None:
            print(f"ERROR: Cannot load test image: {test_image_path}")
            return 1
        print(f"Test image loaded: {test_image_path.name} ({image.shape})")
    except Exception as e:
        print(f"ERROR: Image loading failed: {e}")
        return 1
    
    # Test SAM model loading
    try:
        print("Loading SAM model...")
        sam_model = sam_model_registry["vit_h"](checkpoint=model_path)
        sam_model.to(device="cpu")
        print("SAM model loaded successfully")
        
        # Create mask generator
        mask_generator = SamAutomaticMaskGenerator(
            model=sam_model,
            points_per_side=16,
            pred_iou_thresh=0.7,
            stability_score_thresh=0.8,
            min_mask_region_area=50,
        )
        print("Mask generator created successfully")
        
    except Exception as e:
        print(f"ERROR: SAM model loading failed: {e}")
        return 1
    
    # Test mask generation on small image
    try:
        print("Testing mask generation...")
        # Resize image to speed up testing
        h, w = image.shape[:2]
        if max(h, w) > 1024:
            scale = 1024 / max(h, w)
            new_h, new_w = int(h * scale), int(w * scale)
            image = cv2.resize(image, (new_w, new_h))
            print(f"Resized image to {new_w}x{new_h}")
        
        masks = mask_generator.generate(image)
        print(f"Generated {len(masks)} masks")
        
        if len(masks) > 0:
            areas = [mask['area'] for mask in masks]
            print(f"Mask areas: min={min(areas)}, max={max(areas)}, avg={sum(areas)/len(areas):.0f}")
            print("SUCCESS: SAM is working correctly!")
        else:
            print("WARNING: No masks generated")
            
    except Exception as e:
        print(f"ERROR: Mask generation failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
